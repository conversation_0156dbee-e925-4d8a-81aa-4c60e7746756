# 温州人脸识别算法 - 模型文件

## 📝 需要的模型文件

请将以下模型文件放置在此目录下：

### 1. 人脸检测模型
- **文件名**: `scrfd_10g_tykjanimal_240322.onnx`
- **功能**: 基于SCRFD的人脸检测
- **输入尺寸**: 640x640
- **格式**: ONNX

### 2. 人脸识别模型
- **文件名**: `face_feature_Resnet50_zsq_20240201.onnx`
- **功能**: 基于ResNet50的人脸特征提取
- **输入尺寸**: 112x112
- **格式**: ONNX
- **输出**: 512维特征向量

### 3. 人脸质量评估模型
- **文件名**: `PFLD_GhostNet_Slim_112_1_opt_20240117.onnx`
- **功能**: 基于PFLD的人脸质量评估和关键点检测
- **输入尺寸**: 112x112
- **格式**: ONNX

## 📂 目录结构

```
models/
├── README.md                                           # 本文件
├── scrfd_10g_tykjanimal_240322.onnx                   # 人脸检测模型
├── face_feature_Resnet50_zsq_20240201.onnx            # 人脸识别模型
└── PFLD_GhostNet_Slim_112_1_opt_20240117.onnx         # 质量评估模型
```

## ⚠️ 注意事项

1. **模型文件不会被git跟踪** - 这些文件已被添加到.gitignore中
2. **文件名必须完全匹配** - 算法会根据配置文件中的路径加载模型
3. **确保文件完整性** - 损坏的模型文件会导致算法初始化失败
4. **模型版本兼容性** - 请使用指定版本的模型文件以确保兼容性

## 🔧 配置文件

模型路径在 `config.ini` 中配置：

```ini
[FACE_DETECTION]
model_path = models/scrfd_10g_tykjanimal_240322.onnx

[FACE_RECOGNITION]
model_path = models/face_feature_Resnet50_zsq_20240201.onnx

[FACE_QUALITY]
model_path = models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx
```
