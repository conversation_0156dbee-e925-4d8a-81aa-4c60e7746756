"""
算法信息API
"""

from typing import List, Dict, Any
from fastapi import APIRouter
from pydantic import BaseModel

from ..config import settings

router = APIRouter()


class Capabilities(BaseModel):
    """算法能力描述"""
    input_modes: List[str]
    supported_formats: List[str]
    max_file_size_mb: int
    concurrent_requests: int


class ModelInfo(BaseModel):
    """模型信息"""
    framework: str
    model_files: Dict[str, str]
    input_size: List[int]
    classes: List[str]


class Performance(BaseModel):
    """性能指标"""
    avg_inference_time_ms: float
    throughput_fps: float
    memory_usage_mb: int


class AlgorithmInfo(BaseModel):
    """算法信息响应"""
    algorithm_name: str
    algorithm_version: str
    algorithm_type: str
    description: str
    capabilities: Capabilities
    model_info: ModelInfo
    performance: Performance


@router.get("/info", response_model=AlgorithmInfo)
async def get_algorithm_info():
    """
    获取算法信息
    
    返回算法的详细信息，供平台展示和管理
    """
    return AlgorithmInfo(
        algorithm_name=settings.algorithm_name,
        algorithm_version=settings.algorithm_version,
        algorithm_type=settings.algorithm_type,
        description=settings.algorithm_description,
        capabilities=Capabilities(
            input_modes=["file", "batch"],
            supported_formats=["jpg", "jpeg", "png", "bmp"],
            max_file_size_mb=settings.max_file_size_mb,
            concurrent_requests=settings.max_concurrent_tasks
        ),
        model_info=ModelInfo(
            framework="ONNX Runtime",
            model_files={
                "detection": settings.face_detection_model,
                "recognition": settings.face_recognition_model,
                "quality": settings.face_quality_model
            },
            input_size=[640, 640],
            classes=["face"]
        ),
        performance=Performance(
            avg_inference_time_ms=100.0,
            throughput_fps=10.0,
            memory_usage_mb=1024
        )
    )
