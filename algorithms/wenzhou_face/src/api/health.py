"""
健康检查API
"""

import time
from fastapi import APIRouter
from pydantic import BaseModel

router = APIRouter()


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    timestamp: str


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """
    健康检查接口
    
    用于容器健康检查，供Docker/K8s进行存活探针
    """
    return HealthResponse(
        status="ok",
        timestamp=time.strftime("%Y-%m-%dT%H:%M:%S.000Z", time.gmtime())
    )
