"""
人脸检测API
"""

import time
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, File, UploadFile, Form, HTTPException, Depends
from pydantic import BaseModel

# 避免循环导入，在函数内部导入
from ..models.detection import DetectionRequest, DetectionResults, CompareResults

router = APIRouter()


@router.post("/detect", response_model=DetectionResults)
async def detect_faces(
    file: UploadFile = File(...),
    confidence_threshold: Optional[float] = Form(0.7),
    extract_features: bool = Form(True),
    assess_quality: bool = Form(False),
    annotate_image: bool = Form(False)
):
    """
    人脸检测接口
    
    同步处理单个图像文件，检测其中的人脸
    """
    # 验证文件类型 - 通过文件扩展名和content_type双重检查
    allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    file_extension = None
    if file.filename:
        file_extension = '.' + file.filename.split('.')[-1].lower()

    # 检查content_type或文件扩展名
    is_valid_type = False
    if file.content_type and file.content_type.startswith('image/'):
        is_valid_type = True
    elif file_extension and file_extension in allowed_extensions:
        is_valid_type = True

    if not is_valid_type:
        raise HTTPException(status_code=400, detail="不支持的文件类型，请上传图像文件 (支持: jpg, jpeg, png, bmp, tiff, webp)")
    
    # 验证文件大小
    file_content = await file.read()
    if len(file_content) > 100 * 1024 * 1024:  # 100MB
        raise HTTPException(status_code=413, detail="文件过大，最大支持100MB")
    
    # 获取算法引擎
    from ..main import get_algorithm_engine
    engine = get_algorithm_engine()
    
    # 构建请求参数
    parameters = DetectionRequest(
        confidence_threshold=confidence_threshold,
        extract_features=extract_features,
        assess_quality=assess_quality,
        annotate_image=annotate_image
    )
    
    # 执行检测
    results = await engine.detect(file_content, file.filename, parameters)
    
    # 添加时间戳
    results.timestamp = time.strftime("%Y-%m-%dT%H:%M:%S.000Z", time.gmtime())
    
    return results


@router.post("/compare", response_model=CompareResults)
async def compare_faces(
    file1: UploadFile = File(...),
    file2: UploadFile = File(...),
    threshold: Optional[float] = Form(0.6)
):
    """
    人脸比对接口
    
    比对两张图像中的人脸相似度
    """
    # 验证文件类型 - 通过文件扩展名和content_type双重检查
    allowed_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp'}
    for i, file in enumerate([file1, file2], 1):
        file_extension = None
        if file.filename:
            file_extension = '.' + file.filename.split('.')[-1].lower()

        # 检查content_type或文件扩展名
        is_valid_type = False
        if file.content_type and file.content_type.startswith('image/'):
            is_valid_type = True
        elif file_extension and file_extension in allowed_extensions:
            is_valid_type = True

        if not is_valid_type:
            raise HTTPException(status_code=400, detail=f"文件{i}类型不支持，请上传图像文件 (支持: jpg, jpeg, png, bmp, tiff, webp)")
    
    # 读取文件内容
    file1_content = await file1.read()
    file2_content = await file2.read()
    
    # 验证文件大小
    for content, filename in [(file1_content, file1.filename), (file2_content, file2.filename)]:
        if len(content) > 100 * 1024 * 1024:  # 100MB
            raise HTTPException(status_code=413, detail=f"文件 {filename} 过大，最大支持100MB")
    
    # 获取算法引擎
    from ..main import get_algorithm_engine
    engine = get_algorithm_engine()
    
    # 执行比对
    results = await engine.compare_faces(
        file1_content, file2_content,
        file1.filename, file2.filename
    )
    
    # 添加时间戳
    results["timestamp"] = time.strftime("%Y-%m-%dT%H:%M:%S.000Z", time.gmtime())
    
    return CompareResults(**results)


@router.get("/status")
async def get_status():
    """
    获取算法运行状态
    """
    from ..main import get_algorithm_engine
    engine = get_algorithm_engine()
    
    return {
        "status": "ready" if engine.is_initialized else "loading",
        "message": "算法就绪，可接受请求" if engine.is_initialized else "算法加载中",
        "uptime_seconds": 0,  # 可以添加实际的运行时间统计
        "current_load": {
            "active_tasks": 0,
            "queue_length": 0,
            "cpu_percent": 0.0,
            "memory_used_mb": 0,
            "gpu_memory_used_mb": 0,
            "gpu_memory_total_mb": 0
        },
        "statistics": {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "avg_response_time_ms": 0
        }
    }
