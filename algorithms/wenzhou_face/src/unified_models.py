"""
统一响应格式模型 - v2.0规范
用于温州人脸识别算法
"""

import json
from typing import List, Dict, Any, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime


class Detection(BaseModel):
    """单个检测结果 - 符合v2.0统一规范"""
    # 必选字段
    bbox: List[float] = Field(..., description="边界框坐标 [x_min, y_min, x_max, y_max]")
    confidence: float = Field(..., ge=0.0, le=1.0, description="检测结果的置信度")
    label: str = Field(..., description="类别名称")
    class_id: Optional[int] = Field(None, description="类别的数字ID")
    
    # 可选的通用几何信息
    center: Optional[List[float]] = Field(None, description="中心点坐标 [x, y]")
    width: Optional[float] = Field(None, description="边界框宽度")
    height: Optional[float] = Field(None, description="边界框高度")
    
    # 扩展属性 - 用于容纳算法特有数据
    attributes: Optional[Dict[str, Any]] = Field(default_factory=dict, description="算法特有的扩展数据")
    
    def __init__(self, **data):
        super().__init__(**data)
        # 自动计算几何信息
        if self.bbox and len(self.bbox) == 4:
            x_min, y_min, x_max, y_max = self.bbox
            if self.center is None:
                self.center = [(x_min + x_max) / 2, (y_min + y_max) / 2]
            if self.width is None:
                self.width = x_max - x_min
            if self.height is None:
                self.height = y_max - y_min


class ConfidenceStats(BaseModel):
    """置信度统计"""
    min: float = Field(..., description="最小置信度")
    max: float = Field(..., description="最大置信度")
    avg: float = Field(..., description="平均置信度")


class DetectionSummary(BaseModel):
    """检测结果摘要 - v2.0格式"""
    num_detections: int = Field(..., description="检测到的对象总数")
    class_counts: Dict[str, int] = Field(..., description="各类别的数量统计")
    confidence_stats: Optional[ConfidenceStats] = Field(None, description="置信度统计")


class ModelInfo(BaseModel):
    """模型信息"""
    name: str = Field(..., description="模型名称")
    version: str = Field(..., description="模型版本")


class HardwareInfo(BaseModel):
    """硬件信息"""
    device: str = Field(..., description="计算设备")
    memory_used_mb: Optional[float] = Field(None, description="内存使用量(MB)")


class Metadata(BaseModel):
    """响应元数据"""
    processing_time_ms: float = Field(..., description="处理耗时(毫秒)")
    image_shape: List[int] = Field(..., description="图像尺寸 [height, width, channels]")
    timestamp_utc: str = Field(..., description="UTC时间戳")
    model_info: Optional[ModelInfo] = Field(None, description="模型信息")
    hardware_info: Optional[HardwareInfo] = Field(None, description="硬件信息")


class DetectionData(BaseModel):
    """检测数据部分"""
    detections: List[Detection] = Field(..., description="检测结果列表")
    summary: DetectionSummary = Field(..., description="结果摘要")
    output_files: Optional[Dict[str, str]] = Field(None, description="输出文件URL")


class FaceComparisonData(BaseModel):
    """人脸比对数据"""
    similarity_score: float = Field(..., ge=0.0, le=1.0, description="相似度分数")
    is_same_person: bool = Field(..., description="是否为同一人")
    threshold: float = Field(..., description="判断阈值")
    face1_quality: Optional[float] = Field(None, description="人脸1质量分数")
    face2_quality: Optional[float] = Field(None, description="人脸2质量分数")


class ErrorInfo(BaseModel):
    """错误信息"""
    code: str = Field(..., description="错误代码")
    message: str = Field(..., description="用户友好的错误消息")
    details: Optional[str] = Field(None, description="详细错误信息")
    timestamp: str = Field(..., description="错误发生时间")


class UnifiedResponse(BaseModel):
    """统一响应格式 - v2.0规范"""
    success: bool = Field(..., description="是否成功执行")
    error: Optional[ErrorInfo] = Field(None, description="错误信息，成功时为null")
    data: Optional[Union[DetectionData, FaceComparisonData, Dict[str, Any]]] = Field(None, description="核心业务数据，失败时为null")
    metadata: Metadata = Field(..., description="处理元数据和诊断信息")


# 工具函数
def create_success_response(
    data: Union[DetectionData, FaceComparisonData, Dict[str, Any]],
    metadata: Metadata
) -> UnifiedResponse:
    """创建成功响应"""
    return UnifiedResponse(
        success=True,
        error=None,
        data=data,
        metadata=metadata
    )


def create_error_response(
    error_code: str,
    error_message: str,
    error_details: Optional[str] = None,
    metadata: Optional[Metadata] = None
) -> UnifiedResponse:
    """创建错误响应"""
    error_info = ErrorInfo(
        code=error_code,
        message=error_message,
        details=error_details,
        timestamp=datetime.utcnow().isoformat() + "Z"
    )
    
    if metadata is None:
        metadata = Metadata(
            processing_time_ms=0.0,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z"
        )
    
    return UnifiedResponse(
        success=False,
        error=error_info,
        data=None,
        metadata=metadata
    )


def convert_face_detection_to_v2(legacy_result: Dict[str, Any]) -> DetectionData:
    """将旧格式人脸检测结果转换为v2.0格式"""
    detections = []
    
    for face in legacy_result.get('faces', []):
        # 转换边界框格式
        bbox = face.get('bbox', [])
        if isinstance(bbox, dict):
            # 旧格式: {"x1": x1, "y1": y1, "x2": x2, "y2": y2}
            bbox = [bbox.get('x1', 0), bbox.get('y1', 0), bbox.get('x2', 0), bbox.get('y2', 0)]
        elif isinstance(bbox, list) and len(bbox) == 4:
            # 已经是数组格式
            pass
        else:
            continue  # 跳过无效的检测结果
        
        # 构建扩展属性
        attributes = {}
        if 'landmarks' in face:
            attributes['landmarks'] = face['landmarks']
        if 'quality_score' in face:
            attributes['quality_score'] = face['quality_score']
        if 'age' in face:
            attributes['age'] = face['age']
        if 'gender' in face:
            attributes['gender'] = face['gender']
        if 'emotion' in face:
            attributes['emotion'] = face['emotion']
        if 'feature_vector' in face:
            attributes['feature_vector'] = face['feature_vector']
        
        detection = Detection(
            bbox=bbox,
            confidence=face.get('confidence', 0.0),
            label="face",
            class_id=0,
            attributes=attributes
        )
        detections.append(detection)
    
    # 计算置信度统计
    confidences = [d.confidence for d in detections]
    confidence_stats = None
    if confidences:
        confidence_stats = ConfidenceStats(
            min=min(confidences),
            max=max(confidences),
            avg=sum(confidences) / len(confidences)
        )
    
    summary = DetectionSummary(
        num_detections=len(detections),
        class_counts={"face": len(detections)},
        confidence_stats=confidence_stats
    )
    
    return DetectionData(
        detections=detections,
        summary=summary
    )
