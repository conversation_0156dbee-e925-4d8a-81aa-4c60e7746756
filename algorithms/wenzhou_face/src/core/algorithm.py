"""
温州人脸识别算法引擎 - 标准化封装
"""

import os
import time
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path

import numpy as np
from PIL import Image
import io

from ..config import settings
from ..inference_engine import WenzhouFaceEngine as OriginalEngine
from ..models.detection import DetectionRequest, DetectionResults, Detection, BoundingBox, Point


class WenzhouFaceEngine:
    """温州人脸识别算法引擎 - 标准化封装"""
    
    def __init__(self):
        self.original_engine: Optional[OriginalEngine] = None
        self.is_initialized = False
        
    async def initialize(self):
        """初始化算法引擎"""
        try:
            # 初始化原始引擎
            config_path = "config.ini"
            self.original_engine = OriginalEngine(config_path)
            self.is_initialized = True
            print(f"✅ 温州人脸识别引擎初始化完成")
            
        except Exception as e:
            print(f"❌ 算法引擎初始化失败: {e}")
            raise e
    
    async def cleanup(self):
        """清理资源"""
        self.is_initialized = False
        self.original_engine = None
    
    def _parse_image(self, file_content: bytes) -> np.ndarray:
        """解析图像数据"""
        try:
            image = Image.open(io.BytesIO(file_content))
            # 转换为RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            return np.array(image)
        except Exception as e:
            raise ValueError(f"无法解析图像: {e}")
    
    def _convert_results(self, original_results: Dict) -> List[Detection]:
        """转换原始结果为标准格式"""
        detections = []

        for face in original_results.get('faces', []):
            # bbox是一个列表 [x_min, y_min, x_max, y_max, confidence]
            bbox_list = face.get('bbox', [])
            if len(bbox_list) < 4:
                continue

            x_min, y_min, x_max, y_max = bbox_list[:4]
            confidence = bbox_list[4] if len(bbox_list) > 4 else face.get('confidence', 0.0)

            width = x_max - x_min
            height = y_max - y_min

            detection = Detection(
                class_id=0,
                class_name="face",
                confidence=float(confidence),
                bbox=BoundingBox(
                    x_min=int(x_min),
                    y_min=int(y_min),
                    x_max=int(x_max),
                    y_max=int(y_max),
                    width=int(width),
                    height=int(height)
                ),
                center=Point(
                    x=int(x_min + width / 2),
                    y=int(y_min + height / 2)
                ),
                # 人脸特有属性
                landmarks=face.get('landmarks', []),
                feature_vector=face.get('feature', []),
                quality_score=face.get('quality', {}).get('overall_score', 0.0) if isinstance(face.get('quality'), dict) else 0.0
            )
            detections.append(detection)

        return detections
    
    async def detect(
        self, 
        file_content: bytes, 
        filename: str, 
        parameters: DetectionRequest
    ) -> DetectionResults:
        """执行人脸检测"""
        if not self.is_initialized or not self.original_engine:
            raise RuntimeError("算法引擎未初始化")
        
        start_time = time.time()
        
        try:
            # 解析图像
            image = self._parse_image(file_content)
            
            # 执行推理
            results = self.original_engine.process_image(
                image,
                extract_features=parameters.extract_features,
                assess_quality=parameters.assess_quality
            )
            
            # 转换结果格式
            detections = self._convert_results(results)
            
            processing_time = time.time() - start_time
            
            return DetectionResults(
                success=True,
                processing_time_ms=processing_time * 1000,
                results={
                    "detections": [d.dict() for d in detections],
                    "summary": {
                        "total_detections": len(detections),
                        "classes_detected": ["face"] if detections else [],
                        "image_size": {
                            "width": image.shape[1],
                            "height": image.shape[0]
                        }
                    }
                }
            )
            
        except Exception as e:
            processing_time = time.time() - start_time
            return DetectionResults(
                success=False,
                processing_time_ms=processing_time * 1000,
                error=str(e),
                results=None
            )
    
    async def compare_faces(
        self,
        file1_content: bytes,
        file2_content: bytes,
        filename1: str,
        filename2: str
    ) -> Dict[str, Any]:
        """人脸比对"""
        if not self.is_initialized or not self.original_engine:
            raise RuntimeError("算法引擎未初始化")
        
        start_time = time.time()
        
        try:
            # 解析图像
            image1 = self._parse_image(file1_content)
            image2 = self._parse_image(file2_content)
            
            # 提取特征
            results1 = self.original_engine.process_image(image1, extract_features=True)
            results2 = self.original_engine.process_image(image2, extract_features=True)
            
            if not results1['faces'] or not results2['faces']:
                raise ValueError("未检测到人脸")
            
            # 获取特征向量
            feature1 = np.array(results1['faces'][0]['feature'])
            feature2 = np.array(results2['faces'][0]['feature'])
            
            # 计算相似度
            similarity = self.original_engine.compare_faces(feature1, feature2)
            is_same_person = similarity >= settings.similarity_threshold
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "processing_time_ms": processing_time * 1000,
                "results": {
                    "similarity": float(similarity),
                    "threshold": settings.similarity_threshold,
                    "is_same_person": is_same_person,
                    "faces_in_image1": len(results1['faces']),
                    "faces_in_image2": len(results2['faces'])
                }
            }
            
        except Exception as e:
            processing_time = time.time() - start_time
            return {
                "success": False,
                "processing_time_ms": processing_time * 1000,
                "error": str(e),
                "results": None
            }
