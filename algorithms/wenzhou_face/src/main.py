#!/usr/bin/env python3
"""
温州人脸识别算法主应用 - 遵循统一API规范
"""

import os
import asyncio
from contextlib import asynccontextmanager
from pathlib import Path

from fastapi import FastAPI, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

from .config import settings
from .api import health, info, detection
from .core.algorithm import WenzhouFaceEngine


# 全局变量
algorithm_engine = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global algorithm_engine
    
    # 启动时初始化
    try:
        # 创建结果目录
        os.makedirs(settings.results_dir, exist_ok=True)
        
        # 初始化算法引擎
        algorithm_engine = WenzhouFaceEngine()
        await algorithm_engine.initialize()
        
        print(f"✅ {settings.algorithm_name} 初始化完成")
        
    except Exception as e:
        print(f"❌ 算法引擎初始化失败: {e}")
        raise e
    
    yield
    
    # 关闭时清理
    if algorithm_engine:
        await algorithm_engine.cleanup()
        print("🔄 算法引擎已清理")


# 创建FastAPI应用
app = FastAPI(
    title=settings.algorithm_name,
    description=settings.algorithm_description,
    version=settings.algorithm_version,
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


def get_algorithm_engine():
    """获取算法引擎实例"""
    if algorithm_engine is None:
        raise HTTPException(status_code=503, detail="算法引擎未初始化")
    return algorithm_engine


# 注册API路由
app.include_router(health.router, prefix="/api/v1", tags=["健康检查"])
app.include_router(info.router, prefix="/api/v1", tags=["算法信息"])
app.include_router(detection.router, prefix="/api/v1", tags=["人脸识别"])

# 静态文件服务
if os.path.exists(settings.static_dir):
    app.mount("/api/v1/static", StaticFiles(directory=settings.static_dir), name="static")

# 根路径
@app.get("/")
async def root():
    return {
        "algorithm_name": settings.algorithm_name,
        "version": settings.algorithm_version,
        "status": "running",
        "docs": "/docs",
        "health": "/api/v1/health"
    }


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.host,
        port=settings.port,
        reload=settings.debug
    )
