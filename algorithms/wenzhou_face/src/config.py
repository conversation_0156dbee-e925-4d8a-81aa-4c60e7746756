#!/usr/bin/env python3
"""
温州人脸识别算法配置
"""

from typing import List
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置"""
    
    # 算法基本信息
    algorithm_name: str = "温州人脸识别算法"
    algorithm_version: str = "1.0.0"
    algorithm_type: str = "人脸识别"
    algorithm_description: str = "基于深度学习的人脸检测、识别、比对和质量评估"
    
    # 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    debug: bool = False
    
    # CORS配置
    cors_origins: List[str] = ["*"]
    
    # 模型配置
    face_detection_model: str = "models/scrfd_10g_tykjanimal_240322.onnx"
    face_recognition_model: str = "models/face_feature_Resnet50_zsq_20240201.onnx"
    face_quality_model: str = "models/PFLD_GhostNet_Slim_112_1_opt_20240117.onnx"
    
    # 检测参数
    confidence_threshold: float = 0.7
    nms_threshold: float = 0.4
    similarity_threshold: float = 0.6
    min_quality_score: float = 0.5
    
    # 处理配置
    max_file_size_mb: int = 100
    max_concurrent_tasks: int = 5
    task_timeout_seconds: int = 300
    
    # 存储配置
    static_dir: str = "static"
    results_dir: str = "static/results"
    
    # 设备配置
    device: str = "auto"  # auto, cpu, cuda
    enable_gpu: bool = True
    
    class Config:
        env_prefix = "WENZHOU_FACE_"


# 全局配置实例
settings = Settings()
