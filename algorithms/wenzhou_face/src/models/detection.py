"""
人脸检测数据模型
"""

from typing import List, Dict, Any, Optional
from pydantic import BaseModel


class Point(BaseModel):
    """点坐标"""
    x: int
    y: int


class BoundingBox(BaseModel):
    """边界框"""
    x_min: int
    y_min: int
    x_max: int
    y_max: int
    width: int
    height: int


class Detection(BaseModel):
    """检测结果"""
    class_id: int
    class_name: str
    confidence: float
    bbox: BoundingBox
    center: Point
    
    # 人脸特有属性
    landmarks: Optional[List[List[float]]] = None
    feature_vector: Optional[List[float]] = None
    quality_score: Optional[float] = None


class DetectionRequest(BaseModel):
    """检测请求参数"""
    confidence_threshold: Optional[float] = 0.7
    extract_features: bool = True
    assess_quality: bool = False
    annotate_image: bool = False


class DetectionResults(BaseModel):
    """检测结果响应"""
    success: bool
    processing_time_ms: float
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: Optional[str] = None


class CompareRequest(BaseModel):
    """人脸比对请求参数"""
    threshold: Optional[float] = 0.6


class CompareResults(BaseModel):
    """人脸比对结果"""
    success: bool
    processing_time_ms: float
    results: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    timestamp: Optional[str] = None
