[project]
name = "wenzhou-face"
version = "1.0.0"
description = "温州人脸识别算法 - 基于深度学习的人脸检测、识别和质量评估"
authors = [
    {name = "Algorithm Team", email = "<EMAIL>"}
]
requires-python = ">=3.11"
dependencies = [
    "torch>=1.9.0",
    "torchvision>=0.10.0",
    "onnxruntime>=1.12.0",
    "opencv-python>=4.5.0",
    "pillow>=8.0.0",
    "numpy>=1.21.0",
    "requests>=2.25.0",
    "pyyaml>=5.4.0",
    "fastapi>=0.68.0",
    "uvicorn>=0.15.0",
    "python-multipart>=0.0.5",
    "scikit-image>=0.18.0",
    "matplotlib>=3.3.0",
    "seaborn>=0.11.0",
    "tqdm>=4.62.0",
    "onnx>=1.17.0",
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["dev"]

[tool.uv]
dev-dependencies = [
    "pytest>=6.0.0",
    "pytest-cov>=2.12.0",
    "black>=21.0.0",
    "flake8>=3.9.0",
    "mypy>=0.910",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
