"""
人车非检测算法 - 日志配置模块
独立的日志系统，支持本地开发和Docker环境
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path


class RenchefeiLogger:
    """人车非检测算法专用日志器"""
    
    def __init__(self, name: str = "renchefei"):
        self.name = name
        self.logger = None
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        # 创建日志器
        self.logger = logging.getLogger(self.name)
        self.logger.setLevel(logging.DEBUG)
        
        # 避免重复添加处理器
        if self.logger.handlers:
            return self.logger
        
        # 确定日志目录
        if os.getenv("IN_DOCKER"):
            log_dir = Path("/app/logs")
            log_level = logging.INFO
        else:
            # 获取当前脚本所在目录的父目录（renchefei目录）
            script_dir = Path(__file__).parent
            project_root = script_dir.parent
            log_dir = project_root / "logs"
            log_level = logging.DEBUG
        
        # 创建日志目录
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建格式器
        detailed_formatter = logging.Formatter(
            fmt='%(asctime)s | %(name)s | %(levelname)s | %(filename)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        simple_formatter = logging.Formatter(
            fmt='%(asctime)s | %(levelname)s | %(message)s',
            datefmt='%H:%M:%S'
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(log_level)
        console_handler.setFormatter(simple_formatter)
        self.logger.addHandler(console_handler)
        
        # 文件处理器 - 详细日志
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_dir / f"{self.name}_detailed.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(file_handler)
        
        # 文件处理器 - 错误日志
        error_handler = logging.handlers.RotatingFileHandler(
            filename=log_dir / f"{self.name}_error.log",
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(detailed_formatter)
        self.logger.addHandler(error_handler)
        
        # 性能日志处理器
        perf_handler = logging.handlers.RotatingFileHandler(
            filename=log_dir / f"{self.name}_performance.log",
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        perf_handler.setLevel(logging.INFO)
        perf_formatter = logging.Formatter(
            fmt='%(asctime)s | PERF | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        perf_handler.setFormatter(perf_formatter)
        
        # 创建性能日志器
        perf_logger = logging.getLogger(f"{self.name}.performance")
        perf_logger.setLevel(logging.INFO)
        perf_logger.addHandler(perf_handler)
        
        return self.logger
    
    def get_logger(self) -> logging.Logger:
        """获取主日志器"""
        return self.logger
    
    def get_performance_logger(self) -> logging.Logger:
        """获取性能日志器"""
        return logging.getLogger(f"{self.name}.performance")
    
    def log_inference_start(self, image_path: str):
        """记录推理开始"""
        self.logger.info(f"开始推理: {image_path}")
    
    def log_inference_result(self, image_path: str, num_detections: int, processing_time: float):
        """记录推理结果"""
        self.logger.info(f"推理完成: {image_path} | 检测数量: {num_detections} | 耗时: {processing_time:.3f}s")
        
        # 记录性能日志
        perf_logger = self.get_performance_logger()
        perf_logger.info(f"image={os.path.basename(image_path)} | detections={num_detections} | time={processing_time:.3f}")
    
    def log_error(self, message: str, exception: Exception = None):
        """记录错误"""
        if exception:
            self.logger.error(f"{message}: {str(exception)}", exc_info=True)
        else:
            self.logger.error(message)
    
    def log_license_check(self, success: bool, message: str = ""):
        """记录授权检查"""
        if success:
            self.logger.info(f"授权验证成功: {message}")
        else:
            self.logger.warning(f"授权验证失败: {message}")
    
    def log_model_loading(self, model_path: str, loading_time: float):
        """记录模型加载"""
        self.logger.info(f"模型加载完成: {model_path} | 耗时: {loading_time:.3f}s")
    
    def log_batch_processing(self, total_images: int, successful: int, failed: int, total_time: float):
        """记录批量处理结果"""
        self.logger.info(f"批量处理完成: 总数={total_images} | 成功={successful} | 失败={failed} | 总耗时={total_time:.3f}s")
        
        # 记录性能日志
        perf_logger = self.get_performance_logger()
        perf_logger.info(f"batch_total={total_images} | batch_success={successful} | batch_failed={failed} | batch_time={total_time:.3f}")


# 全局日志器实例
_logger_instance = None

def get_logger() -> logging.Logger:
    """获取全局日志器实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = RenchefeiLogger()
    return _logger_instance.get_logger()

def get_performance_logger() -> logging.Logger:
    """获取性能日志器实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = RenchefeiLogger()
    return _logger_instance.get_performance_logger()

def get_logger_instance() -> RenchefeiLogger:
    """获取日志器实例"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = RenchefeiLogger()
    return _logger_instance
