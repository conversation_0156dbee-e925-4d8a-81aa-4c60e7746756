"""
人车非检测推理引擎
包含授权验证逻辑的核心推理模块
注意：此文件在交付前需要使用 pyarmor 进行代码混淆处理
"""

import torch
import torch.nn as nn
import numpy as np
import requests
import hashlib
import time
from datetime import datetime, timedelta
from PIL import Image
import torchvision.transforms as transforms
import cv2
import os
import json
import yaml
from pathlib import Path
import torchvision

# 导入日志系统
try:
    from logger_config import get_logger, get_logger_instance
    logger = get_logger()
    logger_instance = get_logger_instance()
except ImportError:
    # 如果日志模块不可用，使用标准 logging
    import logging
    logger = logging.getLogger(__name__)
    logger_instance = None


# 授权验证已移除，算法可直接使用


def letterbox(im, new_shape=(640, 640), color=(114, 114, 114), auto=True, scaleFill=False, scaleup=True, stride=32):
    """调整图像大小并填充以满足stride要求"""
    shape = im.shape[:2]  # current shape [height, width]
    if isinstance(new_shape, int):
        new_shape = (new_shape, new_shape)

    # Scale ratio (new / old)
    r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
    if not scaleup:  # only scale down, do not scale up (for better val mAP)
        r = min(r, 1.0)

    # Compute padding
    ratio = r, r  # width, height ratios
    new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
    dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]  # wh padding
    if auto:  # minimum rectangle
        dw, dh = np.mod(dw, stride), np.mod(dh, stride)  # wh padding
    elif scaleFill:  # stretch
        dw, dh = 0.0, 0.0
        new_unpad = (new_shape[1], new_shape[0])
        ratio = new_shape[1] / shape[1], new_shape[0] / shape[0]  # width, height ratios

    dw /= 2  # divide padding into 2 sides
    dh /= 2

    if shape[::-1] != new_unpad:  # resize
        im = cv2.resize(im, new_unpad, interpolation=cv2.INTER_LINEAR)
    top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
    left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
    im = cv2.copyMakeBorder(im, top, bottom, left, right, cv2.BORDER_CONSTANT, value=color)  # add border
    return im, ratio, (dw, dh)


def non_max_suppression(prediction, conf_thres=0.25, iou_thres=0.45, classes=None, agnostic=False, multi_label=False,
                        labels=(), max_det=300):
    """非极大值抑制"""
    nc = prediction.shape[2] - 5  # number of classes
    xc = prediction[..., 4] > conf_thres  # candidates

    # Settings
    min_wh, max_wh = 2, 4096  # (pixels) minimum and maximum box width and height
    max_nms = 30000  # maximum number of boxes into torchvision.ops.nms()
    time_limit = 10.0  # seconds to quit after
    redundant = True  # require redundant detections
    multi_label &= nc > 1  # multiple labels per box (adds 0.5ms/img)
    merge = False  # use merge-NMS

    t = time.time()
    output = [torch.zeros((0, 6), device=prediction.device)] * prediction.shape[0]
    for xi, x in enumerate(prediction):  # image index, image inference
        x = x[xc[xi]]  # confidence

        # If none remain process next image
        if not x.shape[0]:
            continue

        # Compute conf
        x[:, 5:] *= x[:, 4:5]  # conf = obj_conf * cls_conf

        # Box (center x, center y, width, height) to (x1, y1, x2, y2)
        box = xywh2xyxy(x[:, :4])

        # Detections matrix nx6 (xyxy, conf, cls)
        if multi_label:
            i, j = (x[:, 5:] > conf_thres).nonzero(as_tuple=False).T
            x = torch.cat((box[i], x[i, j + 5, None], j[:, None].float()), 1)
        else:  # best class only
            conf, j = x[:, 5:].max(1, keepdim=True)
            x = torch.cat((box, conf, j.float()), 1)[conf.view(-1) > conf_thres]

        # Filter by class
        if classes is not None:
            x = x[(x[:, 5:6] == torch.tensor(classes, device=x.device)).any(1)]

        # Check shape
        n = x.shape[0]  # number of boxes
        if not n:  # no boxes
            continue
        elif n > max_nms:  # excess boxes
            x = x[x[:, 4].argsort(descending=True)[:max_nms]]  # sort by confidence

        # Batched NMS
        c = x[:, 5:6] * (0 if agnostic else max_wh)  # classes
        boxes, scores = x[:, :4] + c, x[:, 4]  # boxes (offset by class), scores
        i = torchvision.ops.nms(boxes, scores, iou_thres)  # NMS
        if i.shape[0] > max_det:  # limit detections
            i = i[:max_det]

        output[xi] = x[i]
        if (time.time() - t) > time_limit:
            print(f'WARNING: NMS time limit {time_limit}s exceeded')
            break  # time limit exceeded

    return output


def xywh2xyxy(x):
    """Convert nx4 boxes from [x, y, w, h] to [x1, y1, x2, y2] where xy1=top-left, xy2=bottom-right"""
    y = x.clone() if isinstance(x, torch.Tensor) else np.copy(x)
    y[:, 0] = x[:, 0] - x[:, 2] / 2  # top left x
    y[:, 1] = x[:, 1] - x[:, 3] / 2  # top left y
    y[:, 2] = x[:, 0] + x[:, 2] / 2  # bottom right x
    y[:, 3] = x[:, 1] + x[:, 3] / 2  # bottom right y
    return y


def scale_coords(img1_shape, coords, img0_shape, ratio_pad=None):
    """将坐标从img1_shape缩放到img0_shape"""
    if ratio_pad is None:  # calculate from img0_shape
        gain = min(img1_shape[0] / img0_shape[0], img1_shape[1] / img0_shape[1])  # gain  = old / new
        pad = (img1_shape[1] - img0_shape[1] * gain) / 2, (img1_shape[0] - img0_shape[0] * gain) / 2  # wh padding
    else:
        gain = ratio_pad[0][0]
        pad = ratio_pad[1]

    coords[:, [0, 2]] -= pad[0]  # x padding
    coords[:, [1, 3]] -= pad[1]  # y padding
    coords[:, :4] /= gain
    clip_coords(coords, img0_shape)
    return coords


def clip_coords(boxes, shape):
    """将边界框坐标裁剪到图像形状"""
    if isinstance(boxes, torch.Tensor):  # faster individually
        boxes[:, 0].clamp_(0, shape[1])  # x1
        boxes[:, 1].clamp_(0, shape[0])  # y1
        boxes[:, 2].clamp_(0, shape[1])  # x2
        boxes[:, 3].clamp_(0, shape[0])  # y2
    else:  # np.array (faster grouped)
        boxes[:, [0, 2]] = boxes[:, [0, 2]].clip(0, shape[1])  # x1, x2
        boxes[:, [1, 3]] = boxes[:, [1, 3]].clip(0, shape[0])  # y1, y2


class RenchefeiDetectionEngine:
    """人车非检测推理引擎"""
    
    def __init__(self, model_path="models/model_weights.pt"):
        self.model = None

        # 如果是相对路径，转换为相对于项目根目录的路径
        if not os.path.isabs(model_path):
            # 获取当前脚本所在目录的父目录（项目根目录）
            script_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(script_dir)
            self.model_path = os.path.join(project_root, model_path)
        else:
            self.model_path = model_path
        self.class_names = ['person', 'vehicle', 'bicycle', 'plate', 'head', 'fall']
        self.input_size = 640
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.stride = 32
        
        # 检测参数
        self.conf_thres = 0.25
        self.iou_thres = 0.45
        self.max_det = 1000
        
        self._initialize()
    
    def _initialize(self):
        """初始化引擎"""
        # 加载模型
        print("正在加载模型...")
        try:
            self._load_model()
            print("✓ 模型加载成功")
        except Exception as e:
            raise Exception(f"模型加载失败: {e}")
    
    def _load_model(self):
        """加载YOLOv5模型"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"模型文件不存在: {self.model_path}")
        
        # 加载模型 (PyTorch 2.7+ 需要设置 weights_only=False)
        self.model = torch.load(self.model_path, map_location=self.device, weights_only=False)['model'].float()
        self.model.eval()
        
        # 获取模型信息
        if hasattr(self.model, 'names'):
            self.class_names = self.model.names
        if hasattr(self.model, 'stride'):
            self.stride = int(self.model.stride.max())
    
    def _preprocess_image(self, image):
        """图像预处理"""
        if isinstance(image, str):
            # 如果是文件路径
            img0 = cv2.imread(image)
            if img0 is None:
                raise ValueError(f"无法读取图像文件: {image}")
        elif isinstance(image, np.ndarray):
            # 如果是numpy数组
            img0 = image.copy()
        elif isinstance(image, Image.Image):
            # 如果是PIL Image
            img0 = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        else:
            raise ValueError("输入必须是图像文件路径、numpy数组或PIL Image")
        
        # 调整图像大小
        img = letterbox(img0, self.input_size, stride=self.stride, auto=True)[0]
        
        # 转换为RGB并归一化
        img = img.transpose((2, 0, 1))[::-1]  # HWC to CHW, BGR to RGB
        img = np.ascontiguousarray(img)
        img = torch.from_numpy(img).to(self.device)
        img = img.float() / 255.0  # 0 - 255 to 0.0 - 1.0
        if len(img.shape) == 3:
            img = img[None]  # expand for batch dim
            
        return img, img0
    
    def predict(self, image, conf_thres=None, iou_thres=None, classes=None):
        """执行推理预测"""
        start_time = time.time()

        if self.model is None:
            raise Exception("模型未加载，请先完成初始化")

        # 使用传入的参数或默认参数
        conf_thres = conf_thres or self.conf_thres
        iou_thres = iou_thres or self.iou_thres

        # 预处理图像
        img, img0 = self._preprocess_image(image)
        
        # 执行推理
        with torch.no_grad():
            pred = self.model(img)[0]
            
            # 应用NMS
            pred = non_max_suppression(pred, conf_thres, iou_thres, classes, max_det=self.max_det)
        
        # 后处理结果
        detections = []
        for i, det in enumerate(pred):  # detections per image
            if len(det):
                # 将坐标缩放回原图尺寸
                det[:, :4] = scale_coords(img.shape[2:], det[:, :4], img0.shape).round()
                
                # 转换为结果格式
                for *xyxy, conf, cls in reversed(det):
                    x1, y1, x2, y2 = map(int, xyxy)
                    class_id = int(cls)
                    class_name = self.class_names[class_id] if class_id < len(self.class_names) else f'class{class_id}'
                    confidence = float(conf)
                    
                    detection = {
                        'bbox': [x1, y1, x2, y2],
                        'confidence': confidence,
                        'class_id': class_id,
                        'class_name': class_name,
                        'center': [(x1 + x2) // 2, (y1 + y2) // 2],
                        'width': x2 - x1,
                        'height': y2 - y1
                    }
                    detections.append(detection)
        
        result = {
            'detections': detections,
            'image_shape': img0.shape,
            'num_detections': len(detections),
            'class_counts': {}
        }
        
        # 统计各类别数量
        for detection in detections:
            class_name = detection['class_name']
            result['class_counts'][class_name] = result['class_counts'].get(class_name, 0) + 1

        # 计算处理时间
        processing_time = time.time() - start_time
        result['processing_time'] = processing_time

        return result
    
    def batch_predict(self, images, conf_thres=None, iou_thres=None, classes=None):
        """批量预测"""
        results = []
        for image in images:
            try:
                result = self.predict(image, conf_thres, iou_thres, classes)
                results.append(result)
            except Exception as e:
                results.append({'error': str(e)})
        return results
    
    def set_detection_params(self, conf_thres=None, iou_thres=None, max_det=None):
        """设置检测参数"""
        if conf_thres is not None:
            self.conf_thres = conf_thres
        if iou_thres is not None:
            self.iou_thres = iou_thres
        if max_det is not None:
            self.max_det = max_det
