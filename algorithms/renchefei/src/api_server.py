#!/usr/bin/env python3
"""
人车非检测算法 - API服务器 (v2.0统一响应格式)
提供RESTful API接口用于人车非目标检测
"""

import os
import io
import time
from typing import List, Optional
import configparser
from datetime import datetime

import uvicorn
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from PIL import Image
import cv2

# 导入推理引擎
from inference_engine import RenchefeiDetectionEngine
from logger_config import get_logger

# 导入统一响应模型
from unified_models import (
    UnifiedResponse,
    DetectionData,
    Metadata,
    ModelInfo,
    HardwareInfo,
    create_success_response,
    create_error_response,
    convert_legacy_detection_to_v2
)

# 初始化日志
logger = get_logger()

# 创建FastAPI应用
app = FastAPI(
    title="人车非检测算法API v2.0",
    description="基于YOLOv5的人车非目标检测API服务 - 统一响应格式v2.0",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局推理引擎实例
engine: Optional[RenchefeiDetectionEngine] = None


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_file = "config.ini"
    
    try:
        config.read(config_file, encoding='utf-8')
        conf_thres = config.getfloat('DETECTION', 'confidence_threshold', fallback=0.25)
        iou_thres = config.getfloat('DETECTION', 'iou_threshold', fallback=0.45)
        max_det = config.getint('DETECTION', 'max_detections', fallback=1000)
        return conf_thres, iou_thres, max_det
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return 0.25, 0.45, 1000


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化推理引擎"""
    global engine
    try:
        logger.info("正在初始化人车非检测引擎...")
        engine = RenchefeiDetectionEngine()
        
        # 加载配置参数
        conf_thres, iou_thres, max_det = load_config()
        engine.set_detection_params(conf_thres=conf_thres, iou_thres=iou_thres, max_det=max_det)
        
        logger.info("人车非检测引擎初始化完成")
    except Exception as e:
        logger.error(f"推理引擎初始化失败: {e}")
        raise e


@app.get("/api/v1/health", response_model=UnifiedResponse)
async def health_check():
    """健康检查接口 - v2.0格式"""
    start_time = time.time()

    health_data = {
        "service": "人车非检测算法",
        "status": "healthy",
        "engine_loaded": engine is not None
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="renchefei_yolov5",
            version="2.0.0"
        ),
        hardware_info=HardwareInfo(
            device="cpu"  # 这里可以根据实际情况动态获取
        )
    )

    return create_success_response(
        data=health_data,
        metadata=metadata
    )


@app.get("/api/v1/info", response_model=UnifiedResponse)
async def get_algorithm_info():
    """获取算法信息 - v2.0格式"""
    start_time = time.time()

    algorithm_info = {
        "algorithm_name": "人车非检测算法",
        "algorithm_version": "2.0.0",
        "algorithm_type": "目标检测",
        "description": "基于YOLOv5的人车非目标检测算法，支持实时检测和批量处理",
        "capabilities": {
            "input_modes": ["file", "batch"],
            "supported_formats": ["jpg", "jpeg", "png", "bmp"],
            "max_file_size_mb": 100,
            "concurrent_requests": 5
        },
        "model_info": {
            "framework": "PyTorch",
            "model_file": "yolov5s.pt",
            "input_size": [640, 640],
            "classes": ["person", "vehicle", "bicycle", "plate", "head", "fall"]
        },
        "performance": {
            "avg_inference_time_ms": 50,
            "throughput_fps": 20,
            "memory_usage_mb": 512
        }
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="renchefei_yolov5",
            version="2.0.0"
        )
    )

    return create_success_response(
        data=algorithm_info,
        metadata=metadata
    )


@app.post("/api/v1/detect", response_model=UnifiedResponse)
async def detect_objects(
    file: UploadFile = File(...),
    conf_threshold: float = Form(default=None),
    iou_threshold: float = Form(default=None)
):
    """目标检测接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 验证文件类型
        if not file.content_type.startswith('image/'):
            return create_error_response(
                error_code="INVALID_FILE_FORMAT",
                error_message="不支持的文件格式",
                error_details="仅支持图片文件",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))

        # 转换为OpenCV格式
        image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

        # 执行推理
        legacy_results = engine.predict(
            image_cv,
            conf_thres=conf_threshold,
            iou_thres=iou_threshold
        )

        # 转换为v2.0格式
        detection_data = convert_legacy_detection_to_v2(legacy_results)

        # 创建元数据
        processing_time = (time.time() - start_time) * 1000
        metadata = Metadata(
            processing_time_ms=processing_time,
            image_shape=legacy_results.get('image_shape', [0, 0, 0]),
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="renchefei_yolov5",
                version="2.0.0"
            ),
            hardware_info=HardwareInfo(
                device="cpu"
            )
        )

        return create_success_response(
            data=detection_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"目标检测失败: {e}")
        return create_error_response(
            error_code="DETECTION_ERROR",
            error_message="目标检测失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.post("/api/v1/process/batch", response_model=UnifiedResponse)
async def detect_batch(files: List[UploadFile] = File(...)):
    """批量检测接口 - v2.0格式"""
    start_time = time.time()

    try:
        if not engine:
            return create_error_response(
                error_code="ENGINE_NOT_INITIALIZED",
                error_message="推理引擎未初始化",
                metadata=Metadata(
                    processing_time_ms=(time.time() - start_time) * 1000,
                    image_shape=[0, 0, 0],
                    timestamp_utc=datetime.utcnow().isoformat() + "Z"
                )
            )

        batch_results = []
        total_detections = 0

        for i, file in enumerate(files):
            try:
                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)

                # 执行推理
                legacy_result = engine.predict(image_cv)
                detection_data = convert_legacy_detection_to_v2(legacy_result)

                batch_results.append({
                    'filename': file.filename,
                    'index': i,
                    'detections': [d.model_dump() for d in detection_data.detections],
                    'summary': detection_data.summary.model_dump()
                })

                total_detections += detection_data.summary.num_detections

            except Exception as e:
                batch_results.append({
                    'filename': file.filename,
                    'index': i,
                    'error': str(e),
                    'detections': [],
                    'summary': {'num_detections': 0, 'class_counts': {}}
                })

        batch_data = {
            "batch_id": f"batch_{int(time.time())}",
            "total_files": len(files),
            "total_detections": total_detections,
            "results": batch_results
        }

        metadata = Metadata(
            processing_time_ms=(time.time() - start_time) * 1000,
            image_shape=[0, 0, 0],
            timestamp_utc=datetime.utcnow().isoformat() + "Z",
            model_info=ModelInfo(
                name="renchefei_yolov5",
                version="2.0.0"
            )
        )

        return create_success_response(
            data=batch_data,
            metadata=metadata
        )

    except Exception as e:
        logger.error(f"批量检测失败: {e}")
        return create_error_response(
            error_code="BATCH_DETECTION_ERROR",
            error_message="批量检测失败",
            error_details=str(e),
            metadata=Metadata(
                processing_time_ms=(time.time() - start_time) * 1000,
                image_shape=[0, 0, 0],
                timestamp_utc=datetime.utcnow().isoformat() + "Z"
            )
        )


@app.get("/api/v1/status", response_model=UnifiedResponse)
async def get_status():
    """获取运行状态 - v2.0格式"""
    start_time = time.time()

    status_data = {
        "status": "ready" if engine else "loading",
        "message": "模型已加载，准备接受任务" if engine else "模型加载中",
        "uptime_seconds": int(time.time() - start_time),
        "current_load": {
            "active_tasks": 0,
            "queue_length": 0,
            "cpu_percent": 0.0,
            "memory_used_mb": 0,
        },
        "statistics": {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "avg_response_time_ms": 0
        }
    }

    metadata = Metadata(
        processing_time_ms=(time.time() - start_time) * 1000,
        image_shape=[0, 0, 0],
        timestamp_utc=datetime.utcnow().isoformat() + "Z",
        model_info=ModelInfo(
            name="renchefei_yolov5",
            version="2.0.0"
        )
    )

    return create_success_response(
        data=status_data,
        metadata=metadata
    )


if __name__ == "__main__":
    # 启动API服务器 - v2.0
    logger.info("启动人车非检测算法API服务器 v2.0")
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8000,
        reload=False,
        log_level="info"
    )
