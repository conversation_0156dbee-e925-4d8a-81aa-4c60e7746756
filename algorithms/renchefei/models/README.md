# 人车非检测算法 - 模型文件

## 📝 需要的模型文件

请将以下模型文件放置在此目录下：

### 1. YOLOv5检测模型
- **文件名**: `model_weights.pt`
- **功能**: 基于YOLOv5的多目标检测
- **输入尺寸**: 640x640
- **格式**: PyTorch (.pt)
- **检测类别**: 6类 (person, vehicle, bicycle, plate, head, fall)

## 📂 目录结构

```
models/
├── README.md           # 本文件
└── model_weights.pt    # YOLOv5检测模型
```

## 🎯 检测类别

模型支持以下6种目标检测：

| 类别ID | 类别名称 | 英文名 | 说明 |
|--------|----------|--------|------|
| 0 | 人员 | person | 行人检测 |
| 1 | 车辆 | vehicle | 机动车辆检测 |
| 2 | 非机动车 | bicycle | 自行车、电动车检测 |
| 3 | 车牌 | plate | 车牌检测 |
| 4 | 人头 | head | 人头检测 |
| 5 | 跌倒 | fall | 跌倒行为检测 |

## ⚠️ 注意事项

1. **模型文件不会被git跟踪** - 这些文件已被添加到.gitignore中
2. **文件名必须为 `model_weights.pt`** - 算法会根据此文件名加载模型
3. **PyTorch版本兼容性** - 确保模型与当前PyTorch版本兼容
4. **GPU支持** - 模型支持CPU和GPU推理，会自动选择可用设备

## 🔧 配置文件

模型路径在 `config.ini` 中配置：

```ini
[MODEL]
input_size = 640
num_classes = 6
device = auto

[DETECTION]
confidence_threshold = 0.25
iou_threshold = 0.45
max_detections = 1000
```

## 📊 模型性能

- **推理速度**: ~50ms (CPU) / ~10ms (GPU)
- **模型大小**: ~14MB
- **精度**: mAP@0.5 > 0.85
- **支持批量处理**: 是
