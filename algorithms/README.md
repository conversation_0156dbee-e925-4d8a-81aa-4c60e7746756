# 算法容器集合

这个目录包含了算法管理平台支持的所有AI算法容器。每个算法都遵循统一的API规范，确保平台能够统一管理和调用。

## 📁 目录结构

```
algorithms/
├── ALGORITHM_API_SPECIFICATION.md  # 统一API规范文档
├── INTEGRATION_GUIDE.md           # 算法集成指南
├── README.md                       # 本文件
├── template/                       # 算法开发模板
│   ├── Dockerfile
│   ├── requirements.txt
│   ├── src/
│   ├── models/
│   ├── config/
│   ├── build.sh
│   └── README.md
├── renchefei/                      # 人车非检测算法
│   ├── Dockerfile
│   ├── app.py
│   └── requirements.txt
└── [其他算法目录]/
```

## 🎯 核心文档

### 📋 [API规范文档](ALGORITHM_API_SPECIFICATION.md)
定义了所有算法容器必须遵循的统一API接口规范，包括：
- 基础约定和技术栈要求
- 管理与自省API（健康检查、算法信息、运行状态）
- 文件处理API（同步/异步处理）
- 实时流处理API（WebSocket）
- 错误处理和性能要求

### 🚀 [集成指南](INTEGRATION_GUIDE.md)
详细说明如何将新的AI算法集成到平台中，包括：
- 完整的集成流程
- 使用模板快速开发
- 高级配置和优化
- 测试和验证方法
- 故障排除和最佳实践

## 🛠️ 开发模板

### [template/](template/)
提供了一个完整的算法容器开发模板，包含：
- **标准化结构**: 遵循最佳实践的项目结构
- **完整API实现**: 实现了所有必需的API接口
- **配置管理**: 灵活的配置系统
- **异步支持**: 支持同步和异步处理
- **流处理**: WebSocket实时流处理
- **构建脚本**: 自动化构建和测试脚本

### 使用模板快速开始
```bash
# 复制模板
cp -r algorithms/template algorithms/your-algorithm-name
cd algorithms/your-algorithm-name

# 修改配置
vim Dockerfile  # 更新算法标签
vim src/config.py  # 更新算法配置

# 实现算法逻辑
vim src/core/algorithm.py

# 构建和测试
./build.sh all
```

## 📦 现有算法

### [renchefei/](renchefei/) - 人车非检测算法
- **类型**: 目标检测
- **功能**: 检测图像中的人、车、非机动车
- **技术**: YOLOv8 + PyTorch
- **状态**: ✅ 已集成

## 🔧 算法开发规范

### 必需标签
每个算法容器必须包含以下Docker标签：
```dockerfile
LABEL algorithm.platform="true"          # 平台识别标识
LABEL algorithm.name="算法名称"           # 算法显示名称
LABEL algorithm.type="算法类型"           # 目标检测/图像分类/人脸识别等
LABEL algorithm.version="1.0.0"          # 算法版本
LABEL algorithm.description="算法描述"    # 功能描述
```

### 标准端口
- 所有算法容器统一使用 **8000** 端口
- 支持通过环境变量 `PORT` 自定义端口

### API接口要求
所有算法必须实现以下核心接口：
- `GET /api/v1/health` - 健康检查
- `GET /api/v1/info` - 算法信息
- `GET /api/v1/status` - 运行状态
- `POST /api/v1/detect` - 同步检测
- `POST /api/v1/process/async` - 异步处理

## 🚀 快速集成新算法

### 1. 准备工作
- 准备算法模型文件
- 确定算法类型和功能
- 准备测试数据

### 2. 使用模板开发
```bash
# 复制模板
cp -r algorithms/template algorithms/my-algorithm

# 进入目录
cd algorithms/my-algorithm

# 修改配置
# 1. 编辑 Dockerfile 中的标签
# 2. 编辑 src/config.py 中的配置
# 3. 实现 src/core/algorithm.py 中的算法逻辑
# 4. 添加模型文件到 models/ 目录
# 5. 更新 requirements.txt 依赖
```

### 3. 构建和测试
```bash
# 构建镜像
./build.sh build

# 测试功能
./build.sh test

# 完整流程
./build.sh all
```

### 4. 部署到平台
```bash
# 启动容器
docker run -d -p 8001:8000 --name my-algorithm my-algorithm:latest

# 平台会自动发现并管理该算法
```

## 📊 算法类型分类

### 目标检测 (Object Detection)
- 检测图像中的目标对象
- 返回边界框和类别信息
- 示例：人车非检测、安全帽检测

### 图像分类 (Image Classification)
- 对整个图像进行分类
- 返回类别和置信度
- 示例：场景分类、质量检测

### 人脸识别 (Face Recognition)
- 检测和识别人脸
- 返回人脸位置和身份信息
- 示例：人脸考勤、访客识别

### 语义分割 (Semantic Segmentation)
- 像素级别的图像分割
- 返回分割掩码
- 示例：道路分割、医学影像分割

### 姿态估计 (Pose Estimation)
- 检测人体关键点
- 返回关键点坐标
- 示例：运动分析、行为识别

## 🔍 质量保证

### 代码质量
- 遵循PEP 8代码规范
- 使用类型提示
- 添加详细文档和注释
- 单元测试覆盖率 > 80%

### 性能要求
- 健康检查响应时间 < 100ms
- 同步检测响应时间 < 5s
- 支持至少5个并发请求
- 内存使用合理

### 安全要求
- 输入验证和清理
- 错误信息不泄露敏感信息
- 使用非root用户运行
- 资源使用限制

## 📚 参考资源

### 文档
- [FastAPI官方文档](https://fastapi.tiangolo.com/)
- [Docker最佳实践](https://docs.docker.com/develop/dev-best-practices/)
- [OpenAPI规范](https://swagger.io/specification/)

### 工具
- [Postman](https://www.postman.com/) - API测试
- [Docker Desktop](https://www.docker.com/products/docker-desktop) - 容器管理
- [VS Code](https://code.visualstudio.com/) - 代码编辑

### 示例代码
- [PyTorch官方示例](https://github.com/pytorch/examples)
- [TensorFlow模型库](https://github.com/tensorflow/models)
- [OpenCV教程](https://docs.opencv.org/4.x/d6/d00/tutorial_py_root.html)

## 🤝 贡献指南

### 添加新算法
1. Fork项目
2. 使用模板创建新算法
3. 实现算法逻辑
4. 添加测试用例
5. 更新文档
6. 提交Pull Request

### 改进现有算法
1. 创建issue描述问题
2. Fork项目并创建分支
3. 实现改进
4. 添加测试
5. 提交Pull Request

### 文档贡献
- 改进API文档
- 添加使用示例
- 翻译文档
- 修复错误

## 📄 许可证

本项目采用 MIT 许可证。详见各算法目录中的LICENSE文件。

## 🆘 获取帮助

### 常见问题
查看各算法目录中的README.md文件

### 技术支持
- 创建GitHub Issue
- 查看集成指南
- 参考API规范文档

### 社区
- 参与讨论
- 分享经验
- 贡献代码

---

**开始你的算法集成之旅吧！** 🚀
