# 交通事故分类算法 - 模型文件

## 📝 需要的模型文件

请将以下模型文件放置在此目录下：

### 1. 事故分类模型
- **文件名**: `accident_classify_model.onnx`
- **功能**: 基于深度学习的交通事故分类
- **输入尺寸**: 224x224
- **格式**: ONNX
- **分类类别**: 5类事故严重程度

## 📂 目录结构

```
models/
├── README.md                      # 本文件
└── accident_classify_model.onnx   # 事故分类模型
```

## 🎯 分类类别

模型支持以下5种事故严重程度分类：

| 类别ID | 类别名称 | 严重程度 | 说明 |
|--------|----------|----------|------|
| 0 | 轻微事故 | 1 | 无人员伤亡的轻微碰撞 |
| 1 | 一般事故 | 2 | 有轻微人员伤亡 |
| 2 | 重大事故 | 3 | 有重大人员伤亡或财产损失 |
| 3 | 特大事故 | 4 | 造成重大人员伤亡的严重事故 |
| 4 | 非事故 | 0 | 正常交通场景 |

## ⚠️ 注意事项

1. **模型文件不会被git跟踪** - 这些文件已被添加到.gitignore中
2. **文件名必须为 `accident_classify_model.onnx`** - 算法会根据此文件名加载模型
3. **ONNX Runtime版本** - 确保ONNX Runtime版本兼容
4. **输入预处理** - 图像会自动调整为224x224尺寸并进行标准化

## 🔧 配置文件

模型路径在 `config.ini` 中配置：

```ini
[MODEL]
model_path = models/accident_classify_model.onnx
input_size = 224
num_classes = 5
device = auto

[CLASSIFICATION]
confidence_threshold = 0.5
enable_gpu = true
batch_size = 8
```

## 📊 模型性能

- **推理速度**: ~30ms (CPU) / ~5ms (GPU)
- **模型大小**: ~25MB
- **分类精度**: Accuracy > 0.90
- **支持批量处理**: 是

## 🚀 应用场景

- **交通管理**: 事故严重程度自动评估
- **保险理赔**: 事故等级智能判定
- **应急响应**: 快速事故分类和资源调度
- **数据分析**: 交通事故统计和趋势分析
