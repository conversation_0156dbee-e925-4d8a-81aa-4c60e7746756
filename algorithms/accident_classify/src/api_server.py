#!/usr/bin/env python3
"""
交通事故分类算法 - API服务器
提供RESTful API接口用于交通事故类型分类
"""

import os
import io
import time
from typing import List, Optional
import configparser

import uvicorn
import numpy as np
from fastapi import FastAPI, File, UploadFile, HTTPException, Form
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from PIL import Image

# 导入推理引擎
from inference_engine import AccidentClassificationEngine
from logger_config import get_logger

# 初始化日志
logger = get_logger()

# 创建FastAPI应用
app = FastAPI(
    title="交通事故分类算法API",
    description="基于深度学习的交通事故类型分类API服务",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# 全局推理引擎实例
engine: Optional[AccidentClassificationEngine] = None


class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[dict] = None
    error: Optional[str] = None
    timestamp: str
    processing_time: Optional[float] = None


def load_config():
    """加载配置文件"""
    config = configparser.ConfigParser()
    config_file = "config.ini"
    
    try:
        config.read(config_file, encoding='utf-8')
        conf_threshold = config.getfloat('CLASSIFICATION', 'confidence_threshold', fallback=0.5)
        batch_size = config.getint('CLASSIFICATION', 'batch_size', fallback=8)
        return conf_threshold, batch_size
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return 0.5, 8


@app.on_event("startup")
async def startup_event():
    """应用启动时初始化推理引擎"""
    global engine
    try:
        logger.info("正在初始化交通事故分类引擎...")
        config_path = "config.ini"
        engine = AccidentClassificationEngine(config_path)
        logger.info("交通事故分类引擎初始化完成")
    except Exception as e:
        logger.error(f"推理引擎初始化失败: {e}")
        raise e


@app.get("/api/v1/health")
async def health_check():
    """健康检查接口"""
    return APIResponse(
        success=True,
        message="服务运行正常",
        data={
            "service": "交通事故分类算法",
            "status": "healthy",
            "engine_loaded": engine is not None
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.get("/api/v1/info")
async def get_algorithm_info():
    """获取算法信息"""
    return APIResponse(
        success=True,
        message="算法信息获取成功",
        data={
            "name": "交通事故分类算法",
            "version": "1.0.0",
            "description": "基于深度学习的交通事故类型分类算法",
            "capabilities": [
                "事故类型分类",
                "严重程度评估",
                "置信度评分"
            ],
            "classes": [
                "轻微事故",
                "一般事故", 
                "重大事故",
                "特大事故",
                "非事故"
            ],
            "supported_formats": ["jpg", "jpeg", "png", "bmp"]
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.post("/api/v1/classify")
async def classify_accident(
    file: UploadFile = File(...),
    return_all_scores: bool = Form(default=True)
):
    """事故分类接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")
    
    start_time = time.time()
    
    try:
        # 读取上传的图像
        image_data = await file.read()
        image = Image.open(io.BytesIO(image_data))
        
        # 转换为numpy数组
        image_array = np.array(image)
        
        # 执行推理
        results = engine.predict(image_array, return_all_scores=return_all_scores)
        
        processing_time = time.time() - start_time
        
        return APIResponse(
            success=True,
            message=f"分类完成，预测类别: {results['predicted_class']}",
            data=results,
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )
        
    except Exception as e:
        logger.error(f"事故分类失败: {e}")
        return APIResponse(
            success=False,
            message="事故分类失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/classify_batch")
async def classify_batch(files: List[UploadFile] = File(...)):
    """批量分类接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    start_time = time.time()
    results = []

    try:
        for i, file in enumerate(files):
            try:
                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_array = np.array(image)

                # 执行推理
                result = engine.predict(image_array, return_all_scores=True)
                result['filename'] = file.filename
                result['index'] = i
                results.append(result)

            except Exception as e:
                results.append({
                    'filename': file.filename,
                    'index': i,
                    'error': str(e),
                    'predicted_class': 'unknown',
                    'confidence': 0.0
                })

        processing_time = time.time() - start_time

        # 统计结果
        class_counts = {}
        for result in results:
            if 'error' not in result:
                class_name = result['predicted_class']
                class_counts[class_name] = class_counts.get(class_name, 0) + 1

        return APIResponse(
            success=True,
            message=f"批量分类完成，处理 {len(files)} 张图像",
            data={
                "total_images": len(files),
                "class_distribution": class_counts,
                "results": results
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"批量分类失败: {e}")
        return APIResponse(
            success=False,
            message="批量分类失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.get("/api/v1/classes")
async def get_supported_classes():
    """获取支持的分类类别"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    return APIResponse(
        success=True,
        message="获取支持的类别成功",
        data={
            "classes": engine.class_names,
            "num_classes": len(engine.class_names),
            "class_descriptions": {
                "轻微事故": "无人员伤亡的轻微碰撞事故",
                "一般事故": "有轻微人员伤亡的交通事故",
                "重大事故": "有重大人员伤亡或财产损失的事故",
                "特大事故": "造成重大人员伤亡的严重事故",
                "非事故": "正常交通场景，无事故发生"
            },
            "severity_levels": {
                "非事故": 0,
                "轻微事故": 1,
                "一般事故": 2,
                "重大事故": 3,
                "特大事故": 4
            }
        },
        timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
    )


@app.post("/api/v1/analyze_severity")
async def analyze_severity(files: List[UploadFile] = File(...)):
    """事故严重程度分析接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    start_time = time.time()

    try:
        severity_mapping = {
            "非事故": 0,
            "轻微事故": 1,
            "一般事故": 2,
            "重大事故": 3,
            "特大事故": 4
        }

        results = []
        severity_scores = []

        for i, file in enumerate(files):
            try:
                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_array = np.array(image)

                # 执行分类
                result = engine.predict(image_array, return_all_scores=True)

                # 计算严重程度分数
                severity_score = severity_mapping.get(result['predicted_class'], 0)
                severity_scores.append(severity_score)

                result.update({
                    'filename': file.filename,
                    'index': i,
                    'severity_score': severity_score,
                    'severity_level': result['predicted_class']
                })
                results.append(result)

            except Exception as e:
                results.append({
                    'filename': file.filename,
                    'index': i,
                    'error': str(e),
                    'severity_score': 0,
                    'severity_level': 'unknown'
                })

        # 计算整体严重程度统计
        if severity_scores:
            avg_severity = sum(severity_scores) / len(severity_scores)
            max_severity = max(severity_scores)
            min_severity = min(severity_scores)

            # 确定整体风险等级
            if avg_severity >= 3:
                risk_level = "高风险"
            elif avg_severity >= 2:
                risk_level = "中风险"
            elif avg_severity >= 1:
                risk_level = "低风险"
            else:
                risk_level = "无风险"
        else:
            avg_severity = max_severity = min_severity = 0
            risk_level = "无数据"

        processing_time = time.time() - start_time

        return APIResponse(
            success=True,
            message=f"严重程度分析完成，处理 {len(files)} 张图像",
            data={
                "total_images": len(files),
                "severity_analysis": {
                    "average_severity": avg_severity,
                    "max_severity": max_severity,
                    "min_severity": min_severity,
                    "risk_level": risk_level
                },
                "class_distribution": {
                    class_name: sum(1 for r in results if r.get('severity_level') == class_name)
                    for class_name in engine.class_names
                },
                "results": results
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"严重程度分析失败: {e}")
        return APIResponse(
            success=False,
            message="严重程度分析失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


@app.post("/api/v1/confidence_analysis")
async def confidence_analysis(files: List[UploadFile] = File(...)):
    """置信度分析接口"""
    if not engine:
        raise HTTPException(status_code=500, detail="推理引擎未初始化")

    start_time = time.time()

    try:
        results = []
        confidences = []

        for i, file in enumerate(files):
            try:
                # 读取图像
                image_data = await file.read()
                image = Image.open(io.BytesIO(image_data))
                image_array = np.array(image)

                # 执行分类
                result = engine.predict(image_array, return_all_scores=True)
                confidences.append(result['confidence'])

                result.update({
                    'filename': file.filename,
                    'index': i
                })
                results.append(result)

            except Exception as e:
                results.append({
                    'filename': file.filename,
                    'index': i,
                    'error': str(e),
                    'confidence': 0.0
                })

        # 计算置信度统计
        if confidences:
            avg_confidence = sum(confidences) / len(confidences)
            max_confidence = max(confidences)
            min_confidence = min(confidences)

            # 置信度分布
            high_confidence = sum(1 for c in confidences if c >= 0.8)
            medium_confidence = sum(1 for c in confidences if 0.5 <= c < 0.8)
            low_confidence = sum(1 for c in confidences if c < 0.5)

            # 可靠性评估
            if avg_confidence >= 0.8:
                reliability = "高可靠性"
            elif avg_confidence >= 0.6:
                reliability = "中等可靠性"
            else:
                reliability = "低可靠性"
        else:
            avg_confidence = max_confidence = min_confidence = 0
            high_confidence = medium_confidence = low_confidence = 0
            reliability = "无数据"

        processing_time = time.time() - start_time

        return APIResponse(
            success=True,
            message=f"置信度分析完成，处理 {len(files)} 张图像",
            data={
                "total_images": len(files),
                "confidence_statistics": {
                    "average_confidence": avg_confidence,
                    "max_confidence": max_confidence,
                    "min_confidence": min_confidence,
                    "reliability_assessment": reliability
                },
                "confidence_distribution": {
                    "high_confidence": high_confidence,
                    "medium_confidence": medium_confidence,
                    "low_confidence": low_confidence
                },
                "results": results
            },
            processing_time=processing_time,
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )

    except Exception as e:
        logger.error(f"置信度分析失败: {e}")
        return APIResponse(
            success=False,
            message="置信度分析失败",
            error=str(e),
            timestamp=time.strftime("%Y-%m-%d %H:%M:%S")
        )


if __name__ == "__main__":
    # 启动API服务器
    uvicorn.run(
        "api_server:app",
        host="0.0.0.0",
        port=8003,
        reload=False,
        log_level="info"
    )
