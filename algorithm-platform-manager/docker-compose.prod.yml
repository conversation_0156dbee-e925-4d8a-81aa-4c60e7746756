# 算法管理平台 - 生产环境 Docker Compose 配置
version: '3.8'

services:
  # 后端服务 - FastAPI + Python
  backend:
    image: algorithm-platform-backend:latest
    container_name: backend
    ports:
      - "8100:8100"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    networks:
      - algorithm-platform-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped

  # 前端服务 - Nginx + Vue.js
  frontend:
    image: algorithm-platform-frontend:latest
    container_name: frontend
    ports:
      - "3000:80"
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - algorithm-platform-network
    restart: unless-stopped

networks:
  algorithm-platform-network:
    driver: bridge

# 可选：如果需要持久化数据
volumes:
  algorithm-data:
    driver: local
