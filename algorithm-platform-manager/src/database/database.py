#!/usr/bin/env python3
"""
数据库模块
提供SQLite数据库的初始化和基本操作
"""

import sqlite3
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

# 数据库文件路径
DB_PATH = Path(__file__).parent.parent.parent / "data" / "platform.db"


async def init_database():
    """初始化数据库"""
    try:
        # 确保数据目录存在
        DB_PATH.parent.mkdir(parents=True, exist_ok=True)
        
        # 在线程池中执行数据库操作
        await asyncio.get_event_loop().run_in_executor(None, _create_tables)
        
        logger.info(f"数据库初始化成功: {DB_PATH}")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def _create_tables():
    """创建数据库表"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        # 容器记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS containers (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                image TEXT,
                status TEXT,
                algorithm_name TEXT,
                algorithm_type TEXT,
                algorithm_version TEXT,
                ports TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 测试记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS test_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                container_name TEXT NOT NULL,
                endpoint TEXT NOT NULL,
                method TEXT NOT NULL,
                status_code INTEGER,
                response_time REAL,
                success BOOLEAN,
                error_message TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 系统监控记录表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS monitoring_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                cpu_usage REAL,
                memory_usage REAL,
                disk_usage REAL,
                container_count INTEGER,
                running_containers INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 平台配置表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS platform_config (
                key TEXT PRIMARY KEY,
                value TEXT,
                description TEXT,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        conn.commit()
        logger.info("数据库表创建成功")
        
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()


class DatabaseManager:
    """数据库管理器"""
    
    @staticmethod
    async def save_container_info(container_info: Dict[str, Any]):
        """保存容器信息"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None, 
                _save_container_info_sync, 
                container_info
            )
        except Exception as e:
            logger.error(f"保存容器信息失败: {e}")
    
    @staticmethod
    async def save_test_record(test_record: Dict[str, Any]):
        """保存测试记录"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                _save_test_record_sync,
                test_record
            )
        except Exception as e:
            logger.error(f"保存测试记录失败: {e}")
    
    @staticmethod
    async def save_monitoring_record(monitoring_data: Dict[str, Any]):
        """保存监控记录"""
        try:
            await asyncio.get_event_loop().run_in_executor(
                None,
                _save_monitoring_record_sync,
                monitoring_data
            )
        except Exception as e:
            logger.error(f"保存监控记录失败: {e}")
    
    @staticmethod
    async def get_test_history(container_name: str = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取测试历史"""
        try:
            return await asyncio.get_event_loop().run_in_executor(
                None,
                _get_test_history_sync,
                container_name,
                limit
            )
        except Exception as e:
            logger.error(f"获取测试历史失败: {e}")
            return []
    
    @staticmethod
    async def get_monitoring_history(hours: int = 24) -> List[Dict[str, Any]]:
        """获取监控历史"""
        try:
            return await asyncio.get_event_loop().run_in_executor(
                None,
                _get_monitoring_history_sync,
                hours
            )
        except Exception as e:
            logger.error(f"获取监控历史失败: {e}")
            return []


def _save_container_info_sync(container_info: Dict[str, Any]):
    """同步保存容器信息"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        labels = container_info.get("labels", {})
        ports_str = str(container_info.get("ports", {}))
        
        cursor.execute('''
            INSERT OR REPLACE INTO containers 
            (id, name, image, status, algorithm_name, algorithm_type, algorithm_version, ports, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            container_info.get("id"),
            container_info.get("name"),
            container_info.get("image"),
            container_info.get("status"),
            labels.get("algorithm_name"),
            labels.get("algorithm_type"),
            labels.get("algorithm_version"),
            ports_str,
            datetime.now().isoformat()
        ))
        
        conn.commit()
        
    except Exception as e:
        logger.error(f"保存容器信息失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()


def _save_test_record_sync(test_record: Dict[str, Any]):
    """同步保存测试记录"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO test_records 
            (container_name, endpoint, method, status_code, response_time, success, error_message)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ''', (
            test_record.get("container_name"),
            test_record.get("endpoint"),
            test_record.get("method"),
            test_record.get("status_code"),
            test_record.get("response_time"),
            test_record.get("success"),
            test_record.get("error_message")
        ))
        
        conn.commit()
        
    except Exception as e:
        logger.error(f"保存测试记录失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()


def _save_monitoring_record_sync(monitoring_data: Dict[str, Any]):
    """同步保存监控记录"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            INSERT INTO monitoring_records 
            (cpu_usage, memory_usage, disk_usage, container_count, running_containers)
            VALUES (?, ?, ?, ?, ?)
        ''', (
            monitoring_data.get("cpu_usage"),
            monitoring_data.get("memory_usage"),
            monitoring_data.get("disk_usage"),
            monitoring_data.get("container_count"),
            monitoring_data.get("running_containers")
        ))
        
        conn.commit()
        
    except Exception as e:
        logger.error(f"保存监控记录失败: {e}")
        conn.rollback()
        raise
    finally:
        conn.close()


def _get_test_history_sync(container_name: str = None, limit: int = 100) -> List[Dict[str, Any]]:
    """同步获取测试历史"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        if container_name:
            cursor.execute('''
                SELECT * FROM test_records 
                WHERE container_name = ? 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (container_name, limit))
        else:
            cursor.execute('''
                SELECT * FROM test_records 
                ORDER BY created_at DESC 
                LIMIT ?
            ''', (limit,))
        
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()
        
        return [dict(zip(columns, row)) for row in rows]
        
    except Exception as e:
        logger.error(f"获取测试历史失败: {e}")
        return []
    finally:
        conn.close()


def _get_monitoring_history_sync(hours: int = 24) -> List[Dict[str, Any]]:
    """同步获取监控历史"""
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    try:
        cursor.execute('''
            SELECT * FROM monitoring_records 
            WHERE created_at >= datetime('now', '-{} hours')
            ORDER BY created_at DESC
        '''.format(hours))
        
        columns = [description[0] for description in cursor.description]
        rows = cursor.fetchall()
        
        return [dict(zip(columns, row)) for row in rows]
        
    except Exception as e:
        logger.error(f"获取监控历史失败: {e}")
        return []
    finally:
        conn.close()
