#!/usr/bin/env python3
"""
Docker容器管理器
负责管理算法容器的生命周期、状态监控等功能
"""

import asyncio
import json
import logging
from typing import List, Dict, Optional, Any
from datetime import datetime

import docker
from docker.errors import DockerException, NotFound, APIError

logger = logging.getLogger(__name__)


class DockerManager:
    """Docker容器管理器"""
    
    def __init__(self):
        """初始化Docker客户端"""
        try:
            self.client = docker.from_env()
            # 测试连接
            self.client.ping()
            logger.info("Docker客户端连接成功")
        except DockerException as e:
            logger.error(f"Docker客户端连接失败: {e}")
            self.client = None
    
    async def list_algorithm_containers(self) -> List[Dict[str, Any]]:
        """列出所有算法容器"""
        if not self.client:
            return []
        
        try:
            # 获取所有容器（包括停止的）
            containers = self.client.containers.list(all=True)
            algorithm_containers = []
            
            for container in containers:
                # 检查是否为算法容器
                labels = container.labels or {}
                if labels.get("algorithm.platform") == "true":
                    container_info = await self._get_container_info(container)
                    algorithm_containers.append(container_info)
            
            return algorithm_containers
            
        except DockerException as e:
            logger.error(f"获取容器列表失败: {e}")
            return []
    
    async def _get_container_info(self, container) -> Dict[str, Any]:
        """获取容器详细信息"""
        try:
            # 刷新容器状态
            container.reload()
            
            labels = container.labels or {}
            
            # 获取端口映射
            ports = {}
            if container.ports:
                for container_port, host_ports in container.ports.items():
                    if host_ports:
                        ports[container_port] = host_ports[0]['HostPort']
            
            # 获取资源使用情况
            stats = None
            try:
                stats_stream = container.stats(stream=False, decode=True)
                stats = await asyncio.get_event_loop().run_in_executor(
                    None, lambda: next(iter([stats_stream]))
                )
            except Exception:
                pass
            
            return {
                "id": container.id[:12],
                "name": container.name,
                "image": container.image.tags[0] if container.image.tags else "unknown",
                "status": container.status,
                "state": container.attrs['State'],
                "created": container.attrs['Created'],
                "ports": ports,
                "labels": {
                    "algorithm.platform": labels.get("algorithm.platform"),
                    "algorithm.name": labels.get("algorithm.name", "未知算法"),
                    "algorithm.type": labels.get("algorithm.type", "未知类型"),
                    "algorithm.version": labels.get("algorithm.version", "未知版本"),
                    "algorithm.description": labels.get("algorithm.description", "无描述")
                },
                "stats": self._format_stats(stats) if stats else None,
                "health": await self._check_container_health(container)
            }
            
        except Exception as e:
            logger.error(f"获取容器信息失败: {e}")
            return {
                "id": container.id[:12],
                "name": container.name,
                "status": "error",
                "error": str(e)
            }
    
    def _format_stats(self, stats: Dict) -> Dict[str, Any]:
        """格式化容器统计信息"""
        try:
            # CPU使用率计算
            cpu_percent = 0.0
            if 'cpu_stats' in stats and 'precpu_stats' in stats:
                cpu_delta = stats['cpu_stats']['cpu_usage']['total_usage'] - \
                           stats['precpu_stats']['cpu_usage']['total_usage']
                system_delta = stats['cpu_stats']['system_cpu_usage'] - \
                              stats['precpu_stats']['system_cpu_usage']
                
                if system_delta > 0:
                    cpu_percent = (cpu_delta / system_delta) * \
                                 len(stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100
            
            # 内存使用情况
            memory_usage = stats.get('memory_stats', {}).get('usage', 0)
            memory_limit = stats.get('memory_stats', {}).get('limit', 0)
            memory_percent = (memory_usage / memory_limit * 100) if memory_limit > 0 else 0
            
            return {
                "cpu_percent": round(cpu_percent, 2),
                "memory_usage": memory_usage,
                "memory_limit": memory_limit,
                "memory_percent": round(memory_percent, 2),
                "network_rx": stats.get('networks', {}).get('eth0', {}).get('rx_bytes', 0),
                "network_tx": stats.get('networks', {}).get('eth0', {}).get('tx_bytes', 0)
            }
            
        except Exception as e:
            logger.error(f"格式化统计信息失败: {e}")
            return {}
    
    async def _check_container_health(self, container) -> Dict[str, Any]:
        """检查容器健康状态"""
        try:
            # 检查容器是否运行
            if container.status != 'running':
                return {"status": "unhealthy", "reason": f"容器状态: {container.status}"}
            
            # 尝试获取健康检查结果
            health = container.attrs.get('State', {}).get('Health', {})
            if health:
                return {
                    "status": health.get('Status', 'unknown'),
                    "failing_streak": health.get('FailingStreak', 0),
                    "log": health.get('Log', [])[-1] if health.get('Log') else None
                }
            
            # 如果没有健康检查，尝试简单的端口检查
            ports = container.ports
            if ports:
                return {"status": "healthy", "reason": "容器运行中且端口开放"}
            
            return {"status": "unknown", "reason": "无法确定健康状态"}
            
        except Exception as e:
            logger.error(f"检查容器健康状态失败: {e}")
            return {"status": "error", "reason": str(e)}
    
    async def start_container(self, container_name: str) -> Dict[str, Any]:
        """启动容器"""
        if not self.client:
            return {"success": False, "error": "Docker客户端未连接"}
        
        try:
            container = self.client.containers.get(container_name)
            container.start()
            
            # 等待容器启动
            await asyncio.sleep(2)
            container.reload()
            
            return {
                "success": True,
                "message": f"容器 {container_name} 启动成功",
                "status": container.status
            }
            
        except NotFound:
            return {"success": False, "error": f"容器 {container_name} 不存在"}
        except APIError as e:
            return {"success": False, "error": f"启动容器失败: {e}"}
    
    async def stop_container(self, container_name: str) -> Dict[str, Any]:
        """停止容器"""
        if not self.client:
            return {"success": False, "error": "Docker客户端未连接"}
        
        try:
            container = self.client.containers.get(container_name)
            container.stop(timeout=10)
            
            return {
                "success": True,
                "message": f"容器 {container_name} 停止成功"
            }
            
        except NotFound:
            return {"success": False, "error": f"容器 {container_name} 不存在"}
        except APIError as e:
            return {"success": False, "error": f"停止容器失败: {e}"}
    
    async def restart_container(self, container_name: str) -> Dict[str, Any]:
        """重启容器"""
        if not self.client:
            return {"success": False, "error": "Docker客户端未连接"}
        
        try:
            container = self.client.containers.get(container_name)
            container.restart(timeout=10)
            
            # 等待容器重启
            await asyncio.sleep(3)
            container.reload()
            
            return {
                "success": True,
                "message": f"容器 {container_name} 重启成功",
                "status": container.status
            }
            
        except NotFound:
            return {"success": False, "error": f"容器 {container_name} 不存在"}
        except APIError as e:
            return {"success": False, "error": f"重启容器失败: {e}"}
    
    async def get_container_logs(self, container_name: str, lines: int = 100) -> Dict[str, Any]:
        """获取容器日志"""
        if not self.client:
            return {"success": False, "error": "Docker客户端未连接"}
        
        try:
            container = self.client.containers.get(container_name)
            logs = container.logs(tail=lines, timestamps=True).decode('utf-8')
            
            return {
                "success": True,
                "logs": logs,
                "lines": len(logs.split('\n'))
            }
            
        except NotFound:
            return {"success": False, "error": f"容器 {container_name} 不存在"}
        except Exception as e:
            return {"success": False, "error": f"获取日志失败: {e}"}
    
    async def remove_container(self, container_name: str, force: bool = False) -> Dict[str, Any]:
        """删除容器"""
        if not self.client:
            return {"success": False, "error": "Docker客户端未连接"}
        
        try:
            container = self.client.containers.get(container_name)
            container.remove(force=force)
            
            return {
                "success": True,
                "message": f"容器 {container_name} 删除成功"
            }
            
        except NotFound:
            return {"success": False, "error": f"容器 {container_name} 不存在"}
        except APIError as e:
            return {"success": False, "error": f"删除容器失败: {e}"}
