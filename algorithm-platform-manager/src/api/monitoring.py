#!/usr/bin/env python3
"""
系统监控API路由
提供系统状态、资源使用、容器监控等功能
"""

import logging
import psutil
import platform
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request
from pydantic import BaseModel
from datetime import datetime

logger = logging.getLogger(__name__)

router = APIRouter()


class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.get("/system", response_model=APIResponse)
async def get_system_info():
    """获取系统信息"""
    try:
        # 系统基本信息
        system_info = {
            "platform": platform.platform(),
            "system": platform.system(),
            "release": platform.release(),
            "version": platform.version(),
            "machine": platform.machine(),
            "processor": platform.processor(),
            "hostname": platform.node(),
            "python_version": platform.python_version(),
            "boot_time": datetime.fromtimestamp(psutil.boot_time()).isoformat()
        }
        
        # CPU信息
        cpu_info = {
            "physical_cores": psutil.cpu_count(logical=False),
            "total_cores": psutil.cpu_count(logical=True),
            "max_frequency": psutil.cpu_freq().max if psutil.cpu_freq() else None,
            "min_frequency": psutil.cpu_freq().min if psutil.cpu_freq() else None,
            "current_frequency": psutil.cpu_freq().current if psutil.cpu_freq() else None,
            "cpu_usage": psutil.cpu_percent(interval=1),
            "per_cpu_usage": psutil.cpu_percent(interval=1, percpu=True)
        }
        
        # 内存信息
        memory = psutil.virtual_memory()
        memory_info = {
            "total": memory.total,
            "available": memory.available,
            "used": memory.used,
            "percentage": memory.percent,
            "total_gb": round(memory.total / (1024**3), 2),
            "available_gb": round(memory.available / (1024**3), 2),
            "used_gb": round(memory.used / (1024**3), 2)
        }
        
        # 磁盘信息
        disk = psutil.disk_usage('/')
        disk_info = {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percentage": (disk.used / disk.total) * 100,
            "total_gb": round(disk.total / (1024**3), 2),
            "used_gb": round(disk.used / (1024**3), 2),
            "free_gb": round(disk.free / (1024**3), 2)
        }
        
        return APIResponse(
            success=True,
            message="系统信息获取成功",
            data={
                "system": system_info,
                "cpu": cpu_info,
                "memory": memory_info,
                "disk": disk_info,
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"获取系统信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")


@router.get("/resources", response_model=APIResponse)
async def get_resource_usage():
    """获取实时资源使用情况"""
    try:
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # 内存使用情况
        memory = psutil.virtual_memory()
        
        # 磁盘使用情况
        disk = psutil.disk_usage('/')
        
        # 网络统计
        network = psutil.net_io_counters()
        
        # 进程数量
        process_count = len(psutil.pids())
        
        return APIResponse(
            success=True,
            message="资源使用情况获取成功",
            data={
                "cpu": {
                    "usage_percent": cpu_percent,
                    "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
                },
                "memory": {
                    "usage_percent": memory.percent,
                    "used_gb": round(memory.used / (1024**3), 2),
                    "available_gb": round(memory.available / (1024**3), 2)
                },
                "disk": {
                    "usage_percent": round((disk.used / disk.total) * 100, 2),
                    "used_gb": round(disk.used / (1024**3), 2),
                    "free_gb": round(disk.free / (1024**3), 2)
                },
                "network": {
                    "bytes_sent": network.bytes_sent,
                    "bytes_recv": network.bytes_recv,
                    "packets_sent": network.packets_sent,
                    "packets_recv": network.packets_recv
                },
                "processes": {
                    "total": process_count
                },
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"获取资源使用情况失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取资源信息失败: {str(e)}")


@router.get("/containers/stats", response_model=APIResponse)
async def get_containers_stats(request: Request):
    """获取所有容器的资源统计"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 统计信息
        total_containers = len(containers)
        running_containers = len([c for c in containers if c.get("status") == "running"])
        stopped_containers = len([c for c in containers if c.get("status") in ["exited", "stopped"]])
        
        # 资源使用统计
        total_cpu = 0
        total_memory = 0
        container_stats = []
        
        for container in containers:
            stats = container.get("stats", {})
            if stats:
                cpu_percent = stats.get("cpu_percent", 0)
                memory_percent = stats.get("memory_percent", 0)
                
                total_cpu += cpu_percent
                total_memory += memory_percent
                
                container_stats.append({
                    "name": container.get("name"),
                    "status": container.get("status"),
                    "cpu_percent": cpu_percent,
                    "memory_percent": memory_percent,
                    "memory_usage_mb": round(stats.get("memory_usage", 0) / (1024*1024), 2),
                    "algorithm_name": container.get("labels", {}).get("algorithm_name", "未知")
                })
        
        return APIResponse(
            success=True,
            message="容器统计信息获取成功",
            data={
                "summary": {
                    "total_containers": total_containers,
                    "running_containers": running_containers,
                    "stopped_containers": stopped_containers,
                    "total_cpu_usage": round(total_cpu, 2),
                    "average_cpu_usage": round(total_cpu / max(running_containers, 1), 2),
                    "total_memory_usage": round(total_memory, 2),
                    "average_memory_usage": round(total_memory / max(running_containers, 1), 2)
                },
                "containers": container_stats,
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"获取容器统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取容器统计失败: {str(e)}")


@router.get("/docker", response_model=APIResponse)
async def get_docker_info(request: Request):
    """获取Docker系统信息"""
    try:
        docker_manager = request.app.state.docker_manager
        
        if not docker_manager.client:
            raise HTTPException(status_code=503, detail="Docker服务不可用")
        
        # 获取Docker系统信息
        docker_info = docker_manager.client.info()
        docker_version = docker_manager.client.version()
        
        # 获取镜像统计
        images = docker_manager.client.images.list()
        
        # 获取所有容器统计
        all_containers = docker_manager.client.containers.list(all=True)
        
        return APIResponse(
            success=True,
            message="Docker信息获取成功",
            data={
                "version": {
                    "version": docker_version.get("Version"),
                    "api_version": docker_version.get("ApiVersion"),
                    "go_version": docker_version.get("GoVersion"),
                    "git_commit": docker_version.get("GitCommit"),
                    "built": docker_version.get("Built")
                },
                "system": {
                    "containers": docker_info.get("Containers", 0),
                    "containers_running": docker_info.get("ContainersRunning", 0),
                    "containers_paused": docker_info.get("ContainersPaused", 0),
                    "containers_stopped": docker_info.get("ContainersStopped", 0),
                    "images": docker_info.get("Images", 0),
                    "server_version": docker_info.get("ServerVersion"),
                    "storage_driver": docker_info.get("Driver"),
                    "total_memory": docker_info.get("MemTotal", 0),
                    "cpu_cores": docker_info.get("NCPU", 0)
                },
                "statistics": {
                    "total_images": len(images),
                    "total_containers": len(all_containers),
                    "algorithm_containers": len(await docker_manager.list_algorithm_containers())
                },
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"获取Docker信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取Docker信息失败: {str(e)}")


@router.get("/health", response_model=APIResponse)
async def get_platform_health(request: Request):
    """获取平台整体健康状态"""
    try:
        # 系统资源检查
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Docker服务检查
        docker_manager = request.app.state.docker_manager
        docker_healthy = docker_manager.client is not None
        
        # 容器状态检查
        containers = await docker_manager.list_algorithm_containers() if docker_healthy else []
        running_containers = len([c for c in containers if c.get("status") == "running"])
        
        # 健康状态评估
        health_issues = []
        
        if cpu_percent > 80:
            health_issues.append("CPU使用率过高")
        
        if memory.percent > 85:
            health_issues.append("内存使用率过高")
        
        if (disk.used / disk.total) * 100 > 90:
            health_issues.append("磁盘空间不足")
        
        if not docker_healthy:
            health_issues.append("Docker服务不可用")
        
        overall_status = "healthy" if not health_issues else "warning" if len(health_issues) <= 2 else "critical"
        
        return APIResponse(
            success=True,
            message="平台健康检查完成",
            data={
                "overall_status": overall_status,
                "health_issues": health_issues,
                "system_resources": {
                    "cpu_usage": cpu_percent,
                    "memory_usage": memory.percent,
                    "disk_usage": round((disk.used / disk.total) * 100, 2)
                },
                "docker_status": {
                    "available": docker_healthy,
                    "total_containers": len(containers),
                    "running_containers": running_containers
                },
                "timestamp": datetime.now().isoformat()
            }
        )
        
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")
