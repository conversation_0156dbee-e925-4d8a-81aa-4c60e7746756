#!/usr/bin/env python3
"""
容器管理API路由
提供Docker容器的检测、管理、监控功能
"""

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request, Query
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()


class ContainerAction(BaseModel):
    """容器操作请求模型"""
    action: str  # start, stop, restart, remove
    force: bool = False


class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.get("/", response_model=APIResponse)
async def list_containers(request: Request):
    """获取所有算法容器列表"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        return APIResponse(
            success=True,
            message=f"成功获取 {len(containers)} 个算法容器",
            data={
                "containers": containers,
                "total": len(containers),
                "running": len([c for c in containers if c.get("status") == "running"]),
                "stopped": len([c for c in containers if c.get("status") in ["exited", "stopped"]])
            }
        )
        
    except Exception as e:
        logger.error(f"获取容器列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取容器列表失败: {str(e)}")


@router.get("/refresh", response_model=APIResponse)
async def refresh_containers(request: Request):
    """刷新容器列表"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        return APIResponse(
            success=True,
            message="容器列表刷新成功",
            data={
                "containers": containers,
                "total": len(containers),
                "timestamp": "2025-07-29"
            }
        )
        
    except Exception as e:
        logger.error(f"刷新容器列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"刷新失败: {str(e)}")


@router.get("/{container_name}", response_model=APIResponse)
async def get_container_info(container_name: str, request: Request):
    """获取指定容器的详细信息"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break
        
        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")
        
        return APIResponse(
            success=True,
            message="容器信息获取成功",
            data=container
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取容器信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取容器信息失败: {str(e)}")


@router.post("/{container_name}/action", response_model=APIResponse)
async def container_action(container_name: str, action: ContainerAction, request: Request):
    """执行容器操作（启动/停止/重启/删除）"""
    try:
        docker_manager = request.app.state.docker_manager
        
        if action.action == "start":
            result = await docker_manager.start_container(container_name)
        elif action.action == "stop":
            result = await docker_manager.stop_container(container_name)
        elif action.action == "restart":
            result = await docker_manager.restart_container(container_name)
        elif action.action == "remove":
            result = await docker_manager.remove_container(container_name, force=action.force)
        else:
            raise HTTPException(status_code=400, detail=f"不支持的操作: {action.action}")
        
        if result["success"]:
            return APIResponse(
                success=True,
                message=result["message"],
                data=result
            )
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"容器操作失败: {e}")
        raise HTTPException(status_code=500, detail=f"容器操作失败: {str(e)}")


@router.get("/{container_name}/logs", response_model=APIResponse)
async def get_container_logs(
    container_name: str, 
    request: Request,
    lines: int = Query(default=100, description="日志行数", ge=1, le=1000)
):
    """获取容器日志"""
    try:
        docker_manager = request.app.state.docker_manager
        result = await docker_manager.get_container_logs(container_name, lines=lines)
        
        if result["success"]:
            return APIResponse(
                success=True,
                message=f"成功获取 {result['lines']} 行日志",
                data={
                    "logs": result["logs"],
                    "lines": result["lines"],
                    "container": container_name
                }
            )
        else:
            raise HTTPException(status_code=404, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取容器日志失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")


@router.get("/{container_name}/health", response_model=APIResponse)
async def check_container_health(container_name: str, request: Request):
    """检查容器健康状态"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break
        
        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")
        
        health_info = container.get("health", {})
        
        return APIResponse(
            success=True,
            message="健康检查完成",
            data={
                "container": container_name,
                "health": health_info,
                "status": container.get("status"),
                "running": container.get("status") == "running"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        raise HTTPException(status_code=500, detail=f"健康检查失败: {str(e)}")


@router.get("/{container_name}/stats", response_model=APIResponse)
async def get_container_stats(container_name: str, request: Request):
    """获取容器资源使用统计"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break
        
        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")
        
        stats = container.get("stats", {})
        
        return APIResponse(
            success=True,
            message="统计信息获取成功",
            data={
                "container": container_name,
                "stats": stats,
                "timestamp": "2025-07-29"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/scan", response_model=APIResponse)
async def scan_algorithm_containers(request: Request):
    """扫描所有算法容器"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 统计信息
        stats = {
            "total": len(containers),
            "running": 0,
            "stopped": 0,
            "error": 0,
            "algorithms": {}
        }
        
        for container in containers:
            status = container.get("status", "unknown")
            if status == "running":
                stats["running"] += 1
            elif status in ["exited", "stopped"]:
                stats["stopped"] += 1
            else:
                stats["error"] += 1
            
            # 统计算法类型
            algorithm_name = container.get("labels", {}).get("algorithm_name", "未知")
            if algorithm_name not in stats["algorithms"]:
                stats["algorithms"][algorithm_name] = 0
            stats["algorithms"][algorithm_name] += 1
        
        return APIResponse(
            success=True,
            message=f"扫描完成，发现 {len(containers)} 个算法容器",
            data={
                "containers": containers,
                "statistics": stats
            }
        )
        
    except Exception as e:
        logger.error(f"扫描容器失败: {e}")
        raise HTTPException(status_code=500, detail=f"扫描失败: {str(e)}")
