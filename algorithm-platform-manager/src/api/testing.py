#!/usr/bin/env python3
"""
在线测试API路由
提供算法容器的在线测试功能
"""

import logging
import aiohttp
import asyncio
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, HTTPException, Request, File, UploadFile, Form
from pydantic import BaseModel

logger = logging.getLogger(__name__)

router = APIRouter()


class APIResponse(BaseModel):
    """统一API响应格式"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


class TestRequest(BaseModel):
    """测试请求模型"""
    container_name: str
    endpoint: str = "/api/v1/detect"
    method: str = "POST"
    parameters: Dict[str, Any] = {}


@router.get("/containers", response_model=APIResponse)
async def get_testable_containers(request: Request):
    """获取可测试的容器列表"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 筛选运行中的容器
        running_containers = []
        for container in containers:
            if container.get("status") == "running" and container.get("ports"):
                # 获取API端口
                ports = container.get("ports", {})
                api_port = None
                for container_port, host_port in ports.items():
                    if "8000" in container_port or "8001" in container_port or "8002" in container_port:
                        api_port = host_port
                        break
                
                if api_port:
                    # 正确获取标签信息
                    labels = container.get("labels", {})
                    algorithm_name = labels.get("algorithm.name", "未知算法")
                    algorithm_type = labels.get("algorithm.type", "未知类型")

                    running_containers.append({
                        "name": container["name"],
                        "id": container["id"],
                        "algorithm_name": algorithm_name,
                        "algorithm_type": algorithm_type,
                        "api_port": api_port,
                        "api_url": f"http://localhost:{api_port}",
                        "health": container.get("health", {})
                    })
        
        return APIResponse(
            success=True,
            message=f"发现 {len(running_containers)} 个可测试容器",
            data={
                "containers": running_containers,
                "total": len(running_containers)
            }
        )
        
    except Exception as e:
        logger.error(f"获取可测试容器失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取容器列表失败: {str(e)}")


@router.get("/containers/{container_name}/info", response_model=APIResponse)
async def get_container_api_info(container_name: str, request: Request):
    """获取容器的API信息"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break
        
        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")
        
        if container.get("status") != "running":
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 未运行")
        
        # 获取API端口
        ports = container.get("ports", {})
        api_port = None
        for container_port, host_port in ports.items():
            if "8000" in container_port or "8001" in container_port or "8002" in container_port:
                api_port = host_port
                break
        
        if not api_port:
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 没有暴露API端口")
        
        api_url = f"http://localhost:{api_port}"
        
        # 尝试获取API信息
        try:
            async with aiohttp.ClientSession() as session:
                # 尝试获取健康检查
                async with session.get(f"{api_url}/api/v1/health", timeout=5) as resp:
                    health_data = await resp.json() if resp.status == 200 else None
                
                # 尝试获取算法信息
                async with session.get(f"{api_url}/api/v1/info", timeout=5) as resp:
                    info_data = await resp.json() if resp.status == 200 else None
                
                # 尝试获取API文档
                docs_available = False
                try:
                    async with session.get(f"{api_url}/docs", timeout=5) as resp:
                        docs_available = resp.status == 200
                except:
                    pass
        
        except Exception as e:
            logger.warning(f"无法连接到容器API: {e}")
            health_data = None
            info_data = None
            docs_available = False
        
        return APIResponse(
            success=True,
            message="容器API信息获取成功",
            data={
                "container": container_name,
                "api_url": api_url,
                "api_port": api_port,
                "health": health_data,
                "info": info_data,
                "docs_available": docs_available,
                "docs_url": f"{api_url}/docs" if docs_available else None,
                "common_endpoints": [
                    "/api/v1/health",
                    "/api/v1/info", 
                    "/api/v1/detect",
                    "/docs"
                ]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取容器API信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取API信息失败: {str(e)}")


@router.post("/containers/{container_name}/test", response_model=APIResponse)
async def test_container_api(
    container_name: str,
    request: Request,
    file: UploadFile = File(None),
    endpoint: str = Form(default="/api/v1/detect"),
    method: str = Form(default="POST"),
    parameters: str = Form(default="{}")
):
    """测试容器API"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break
        
        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")
        
        if container.get("status") != "running":
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 未运行")
        
        # 获取API端口
        ports = container.get("ports", {})
        api_port = None
        for container_port, host_port in ports.items():
            if "8000" in container_port or "8001" in container_port or "8002" in container_port:
                api_port = host_port
                break
        
        if not api_port:
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 没有暴露API端口")
        
        api_url = f"http://localhost:{api_port}{endpoint}"
        
        # 解析参数
        import json
        try:
            params = json.loads(parameters) if parameters != "{}" else {}
        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="参数格式错误，请使用有效的JSON格式")
        
        # 执行API调用
        try:
            async with aiohttp.ClientSession() as session:
                if method.upper() == "GET":
                    async with session.get(api_url, params=params, timeout=30) as resp:
                        response_data = await resp.json()
                        status_code = resp.status
                
                elif method.upper() == "POST":
                    if file:
                        # 文件上传
                        file_content = await file.read()
                        data = aiohttp.FormData()
                        data.add_field('file', file_content, filename=file.filename)
                        
                        # 添加其他参数
                        for key, value in params.items():
                            data.add_field(key, str(value))
                        
                        async with session.post(api_url, data=data, timeout=30) as resp:
                            response_data = await resp.json()
                            status_code = resp.status
                    else:
                        # JSON数据
                        async with session.post(api_url, json=params, timeout=30) as resp:
                            response_data = await resp.json()
                            status_code = resp.status
                else:
                    raise HTTPException(status_code=400, detail=f"不支持的HTTP方法: {method}")
        
        except asyncio.TimeoutError:
            raise HTTPException(status_code=408, detail="API调用超时")
        except aiohttp.ClientError as e:
            raise HTTPException(status_code=500, detail=f"API调用失败: {str(e)}")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"测试执行失败: {str(e)}")
        
        return APIResponse(
            success=True,
            message=f"API测试完成，状态码: {status_code}",
            data={
                "container": container_name,
                "endpoint": endpoint,
                "method": method,
                "status_code": status_code,
                "response": response_data,
                "request_info": {
                    "url": api_url,
                    "parameters": params,
                    "has_file": file is not None,
                    "file_name": file.filename if file else None
                }
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"API测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"测试失败: {str(e)}")


@router.get("/containers/{container_name}/endpoints", response_model=APIResponse)
async def get_container_endpoints(container_name: str, request: Request):
    """获取容器的可用API端点"""
    try:
        docker_manager = request.app.state.docker_manager
        containers = await docker_manager.list_algorithm_containers()
        
        # 查找指定容器
        container = None
        for c in containers:
            if c.get("name") == container_name or c.get("id") == container_name:
                container = c
                break
        
        if not container:
            raise HTTPException(status_code=404, detail=f"容器 {container_name} 不存在")
        
        # 获取API端口
        ports = container.get("ports", {})
        api_port = None
        for container_port, host_port in ports.items():
            if "8000" in container_port or "8001" in container_port or "8002" in container_port:
                api_port = host_port
                break
        
        if not api_port:
            raise HTTPException(status_code=400, detail=f"容器 {container_name} 没有暴露API端口")
        
        api_url = f"http://localhost:{api_port}"
        
        # 预定义的常用端点
        common_endpoints = [
            {
                "path": "/api/v1/health",
                "method": "GET",
                "description": "健康检查",
                "parameters": {}
            },
            {
                "path": "/api/v1/info", 
                "method": "GET",
                "description": "算法信息",
                "parameters": {}
            },
            {
                "path": "/api/v1/detect",
                "method": "POST", 
                "description": "目标检测/算法推理",
                "parameters": {
                    "file": "上传文件",
                    "conf_threshold": "置信度阈值(可选)",
                    "iou_threshold": "IoU阈值(可选)"
                }
            }
        ]
        
        return APIResponse(
            success=True,
            message="端点信息获取成功",
            data={
                "container": container_name,
                "api_url": api_url,
                "endpoints": common_endpoints,
                "docs_url": f"{api_url}/docs"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取端点信息失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取端点失败: {str(e)}")
