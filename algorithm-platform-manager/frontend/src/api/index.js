import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    return config
  },
  error => {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    return response.data
  },
  error => {
    // 对响应错误做点什么
    const message = error.response?.data?.detail || error.message || '请求失败'
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// API接口定义
export const containerAPI = {
  // 获取容器列表
  getContainers: () => api.get('/v1/containers/'),
  
  // 刷新容器列表
  refreshContainers: () => api.get('/v1/containers/refresh'),
  
  // 扫描容器
  scanContainers: () => api.post('/v1/containers/scan'),
  
  // 容器操作
  containerAction: (containerId, action) => 
    api.post(`/v1/containers/${containerId}/action`, { action }),
  
  // 获取容器日志
  getContainerLogs: (containerId, lines = 100) => 
    api.get(`/v1/containers/${containerId}/logs?lines=${lines}`),
  
  // 获取容器统计信息
  getContainerStats: (containerId) => 
    api.get(`/v1/containers/${containerId}/stats`)
}

export const testingAPI = {
  // 获取可测试容器
  getTestingContainers: () => api.get('/v1/testing/containers'),

  // 获取容器API端点
  getContainerEndpoints: (containerName) => api.get(`/v1/testing/containers/${containerName}/endpoints`),

  // 获取容器API信息
  getContainerApiInfo: (containerName) => api.get(`/v1/testing/containers/${containerName}/api`),

  // 执行容器测试
  testContainer: (containerId, testData) => {
    const formData = new FormData()
    if (testData.file) {
      formData.append('file', testData.file)
    }
    formData.append('endpoint', testData.endpoint)
    formData.append('method', testData.method)
    formData.append('parameters', testData.parameters)

    return api.post(`/v1/testing/containers/${containerId}/test`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  }
}

export const monitoringAPI = {
  // 获取系统监控信息
  getSystemInfo: () => api.get('/v1/monitoring/system'),
  
  // 获取资源监控信息
  getResources: () => api.get('/v1/monitoring/resources'),
  
  // 获取Docker信息
  getDockerInfo: () => api.get('/v1/monitoring/docker'),
  
  // 获取健康状态
  getHealth: () => api.get('/v1/monitoring/health')
}

export const platformAPI = {
  // 健康检查
  health: () => api.get('/health'),
  
  // 平台信息
  info: () => api.get('/info')
}

export default api
