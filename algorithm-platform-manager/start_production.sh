#!/bin/bash

# 算法管理平台生产环境启动脚本 (前后端分离架构)

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
    echo -e "${2}[$(date '+%Y-%m-%d %H:%M:%S')] $1${NC}"
}

print_info() {
    print_message "$1" "$BLUE"
}

print_success() {
    print_message "$1" "$GREEN"
}

print_warning() {
    print_message "$1" "$YELLOW"
}

print_error() {
    print_message "$1" "$RED"
}

print_header() {
    print_message "$1" "$PURPLE"
}

# 检查Docker和Docker Compose
check_docker() {
    print_info "检查Docker环境..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker服务未运行，请启动Docker服务"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        print_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    print_success "Docker环境检查通过"
}

# 构建服务
build_services() {
    print_info "构建服务镜像..."
    
    # 构建后端服务
    print_info "构建后端服务..."
    docker-compose build backend
    
    # 构建前端服务
    print_info "构建前端服务..."
    docker-compose build frontend
    
    print_success "服务镜像构建完成"
}

# 启动服务
start_services() {
    print_info "启动算法管理平台服务..."
    
    # 启动所有服务
    docker-compose up -d
    
    print_success "服务启动完成"
    
    # 等待服务就绪
    print_info "等待服务就绪..."
    sleep 10
    
    # 检查服务状态
    check_services_health
}

# 检查服务健康状态
check_services_health() {
    print_info "检查服务健康状态..."
    
    # 检查后端服务
    if curl -f http://localhost:8100/api/health &> /dev/null; then
        print_success "后端服务运行正常"
    else
        print_warning "后端服务可能未完全启动，请稍后检查"
    fi
    
    # 检查前端服务
    if curl -f http://localhost:80 &> /dev/null; then
        print_success "前端服务运行正常"
    else
        print_warning "前端服务可能未完全启动，请稍后检查"
    fi
    
    # 检查Redis服务
    if docker-compose exec -T redis redis-cli ping &> /dev/null; then
        print_success "Redis服务运行正常"
    else
        print_warning "Redis服务可能未完全启动"
    fi
}

# 停止服务
stop_services() {
    print_info "停止算法管理平台服务..."
    docker-compose down
    print_success "服务已停止"
}

# 重启服务
restart_services() {
    print_info "重启算法管理平台服务..."
    docker-compose restart
    print_success "服务已重启"
    
    # 等待服务就绪
    sleep 10
    check_services_health
}

# 查看服务日志
show_logs() {
    local service=${1:-""}
    
    if [ -n "$service" ]; then
        print_info "查看 $service 服务日志..."
        docker-compose logs -f "$service"
    else
        print_info "查看所有服务日志..."
        docker-compose logs -f
    fi
}

# 查看服务状态
show_status() {
    print_info "服务运行状态:"
    docker-compose ps
    
    echo ""
    print_info "服务资源使用情况:"
    docker stats --no-stream $(docker-compose ps -q) 2>/dev/null || true
}

# 清理资源
cleanup() {
    print_warning "清理算法管理平台资源..."
    
    # 停止并删除容器
    docker-compose down -v
    
    # 删除镜像（可选）
    read -p "是否删除构建的镜像? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose down --rmi all -v
        print_success "镜像已删除"
    fi
    
    print_success "资源清理完成"
}

# 显示帮助信息
show_help() {
    print_header "算法管理平台生产环境管理脚本"
    echo ""
    echo "用法: $0 [命令] [参数]"
    echo ""
    echo "命令:"
    echo "  build         构建服务镜像"
    echo "  start         启动所有服务 (默认)"
    echo "  stop          停止所有服务"
    echo "  restart       重启所有服务"
    echo "  status        查看服务状态"
    echo "  logs [服务名]  查看服务日志"
    echo "  cleanup       清理所有资源"
    echo "  help          显示帮助信息"
    echo ""
    echo "服务名:"
    echo "  frontend      前端服务 (Nginx + Vue.js)"
    echo "  backend       后端服务 (FastAPI)"
    echo "  redis         Redis缓存服务"
    echo ""
    echo "访问地址:"
    echo "  前端界面: http://localhost"
    echo "  后端API:  http://localhost:8100/api"
    echo "  API文档:  http://localhost:8100/api/docs"
    echo ""
    echo "示例:"
    echo "  $0 build && $0 start    # 构建并启动"
    echo "  $0 logs backend         # 查看后端日志"
    echo "  $0 status               # 查看服务状态"
    echo ""
}

# 主函数
main() {
    case "${1:-start}" in
        "build")
            check_docker
            build_services
            ;;
        "start")
            check_docker
            start_services
            print_header "🎉 算法管理平台启动完成!"
            echo ""
            print_info "访问地址:"
            echo "  前端界面: http://localhost"
            echo "  后端API:  http://localhost:8100/api"
            echo "  API文档:  http://localhost:8100/api/docs"
            echo ""
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "未知命令: $1"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
