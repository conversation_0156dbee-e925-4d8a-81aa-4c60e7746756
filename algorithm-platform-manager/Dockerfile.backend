# 算法管理平台 - 后端服务 (FastAPI)
FROM python:3.11-slim

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV UV_SYSTEM_PYTHON=1

# 设置工作目录
WORKDIR /app

# 安装系统依赖和uv
RUN apt-get update && apt-get install -y \
    curl \
    gcc \
    python3-dev \
    gosu \
    && rm -rf /var/lib/apt/lists/* \
    && pip install --no-cache-dir uv

# 复制项目配置文件
COPY pyproject.toml uv.lock ./

# 安装Python依赖
RUN uv sync --frozen --no-dev

# 复制源代码和启动脚本
COPY src/ ./src/
COPY docker-entrypoint.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/docker-entrypoint.sh

# 创建非root用户和必要目录
# 创建docker组并将appuser添加到docker组以访问Docker socket
RUN groupadd -r appuser && useradd -r -g appuser appuser \
    && groupadd -f docker \
    && usermod -aG docker appuser \
    && mkdir -p /home/<USER>/.cache/uv \
    && chown -R appuser:appuser /app /home/<USER>

# 暴露端口
EXPOSE 8100

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s \
    CMD curl -f http://localhost:8100/api/health || exit 1

# 临时以root用户运行以解决Docker socket权限问题
# 生产环境建议使用更安全的权限配置
CMD ["uv", "run", "python", "-m", "uvicorn", "src.main:app", "--host", "0.0.0.0", "--port", "8100"]
