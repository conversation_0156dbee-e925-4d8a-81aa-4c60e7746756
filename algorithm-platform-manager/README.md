# 算法管理平台 v2.0

一个采用现代化前后端分离架构的专业AI算法容器管理平台，提供算法容器的统一管理、在线测试、系统监控等功能。

## ✨ 核心特性

- 🐳 **智能容器发现**: 自动扫描和识别算法容器
- 🎯 **精准标签过滤**: 基于 `algorithm.platform="true"` 标签精确识别算法容器
- 🚀 **容器生命周期管理**: 启动、停止、重启、删除容器
- 📊 **实时监控**: 系统资源、容器状态、性能指标监控
- 🧪 **在线测试**: 支持文件上传的算法API测试
- 📋 **日志管理**: 实时查看容器运行日志
- 🔍 **健康检查**: 自动检测容器和服务健康状态
- 🎨 **现代化界面**: Vue.js + Element Plus 响应式界面

## 🏗️ 现代化三层架构

### 表现层 (Front-end Plane)
```
Nginx + Vue.js 3 + Element Plus
├── 响应式UI界面
├── 实时数据展示
├── 交互式图表 (ECharts)
└── 移动端适配
```

### 控制层 (Back-end Control Plane)  
```
FastAPI + Python + uv
├── RESTful API服务
├── Docker容器管理
├── 系统资源监控
├── Redis缓存支持
└── 自动API文档生成
```

### 执行层 (AI Execution Plane)
```
算法容器集群
├── 标准化API接口
├── 健康检查机制
├── 资源使用监控
└── 统一标签管理
```

## 🚀 技术栈

### 前端技术栈
- **框架**: Vue.js 3 (Composition API)
- **UI库**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **图表**: ECharts + Vue-ECharts
- **HTTP客户端**: Axios

### 后端技术栈
- **框架**: FastAPI
- **语言**: Python 3.11+
- **包管理**: uv (极速包管理器)
- **异步**: asyncio + aiohttp
- **容器管理**: Docker API
- **系统监控**: psutil
- **缓存**: Redis
- **配置管理**: Pydantic Settings

### 基础设施
- **Web服务器**: Nginx
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx (API代理)
- **网络**: Docker Bridge Network

## 📦 快速开始

### 前置要求
- Python 3.11+
- Node.js 18+
- Docker 20.10+
- Docker Compose 2.0+
- uv 包管理器

### 安装uv
```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"
```

### 开发环境启动

#### 后端开发
```bash
cd algorithm-platform-manager

# 同步依赖
uv sync --dev

# 启动后端服务
uv run python -m uvicorn src.main:app --host 0.0.0.0 --port 8100 --reload
```

#### 前端开发
```bash
cd algorithm-platform-manager/frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 生产环境部署
```bash
cd algorithm-platform-manager

# 构建并启动所有服务
./start_production.sh build
./start_production.sh start
```

## 🌐 访问地址

- **前端界面**: http://localhost (生产环境)
- **前端开发**: http://localhost:3000 (开发环境)
- **后端API**: http://localhost:8100/api
- **API文档**: http://localhost:8100/api/docs
- **Redis**: localhost:6379

## 📋 管理命令

### 开发环境
```bash
# 后端开发
./start_dev.sh start        # 启动开发服务器
./start_dev.sh test         # 运行测试
./start_dev.sh format       # 代码格式化
./start_dev.sh lint         # 代码检查

# 前端开发
cd frontend
npm run dev                 # 启动开发服务器
npm run build               # 构建生产版本
npm run lint                # 代码检查
```

### 生产环境
```bash
./start_production.sh build     # 构建服务镜像
./start_production.sh start     # 启动所有服务
./start_production.sh stop      # 停止所有服务
./start_production.sh restart   # 重启所有服务
./start_production.sh status    # 查看服务状态
./start_production.sh logs      # 查看服务日志
./start_production.sh cleanup   # 清理所有资源
```

## 🐳 算法容器标准

### 必需标签
所有算法容器必须包含以下Docker标签：

```dockerfile
# 算法容器标识 (必需)
LABEL algorithm.platform="true"

# 算法信息 (推荐)
LABEL algorithm.name="算法名称"
LABEL algorithm.type="算法类型"
LABEL algorithm.version="1.0.0"
LABEL algorithm.description="算法描述"
```

### 标准API接口
算法容器应提供以下标准接口：

```python
# 健康检查 (必需)
GET /api/v1/health

# 算法信息 (推荐)
GET /api/v1/info

# 核心算法接口 (自定义)
POST /api/v1/detect
POST /api/v1/predict
# ... 其他算法接口
```

## 📊 监控指标

### 系统监控
- CPU使用率
- 内存使用率
- 磁盘使用率
- 网络I/O
- 系统负载

### 容器监控
- 容器状态
- 资源使用
- 健康检查
- 日志输出
- 端口映射

### 应用监控
- API响应时间
- 请求成功率
- 错误统计
- 并发连接数

## 🔧 配置说明

### 环境变量
```bash
# API配置
API_HOST=0.0.0.0
API_PORT=8100

# CORS配置
CORS_ORIGINS=http://localhost,http://localhost:3000,http://localhost:80

# Redis配置
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_DB=0

# 应用配置
APP_NAME=算法管理平台
APP_VERSION=1.0.0
DEBUG=false
```

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🆕 版本历史

### v2.0.0 (当前版本)
- ✨ 全新前后端分离架构
- 🎨 Vue.js 3 + Element Plus 现代化界面
- 🚀 FastAPI + uv 高性能后端
- 📊 ECharts 交互式图表
- 🔄 Redis 缓存支持
- 🐳 Docker Compose 一键部署

### v1.0.0
- 🎯 基础容器管理功能
- 📊 系统监控
- 🧪 在线测试
- 🎨 Bootstrap 界面
