# 算法管理平台 - Docker Compose 配置
version: '3.8'

services:
  # 前端服务 - Nginx + Vue.js 构建产物
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: algorithm-platform-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - algorithm-platform
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
    environment:
      - NODE_ENV=production

  # 后端服务 - FastAPI
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    container_name: algorithm-platform-backend
    ports:
      - "8100:8100"
    networks:
      - algorithm-platform
    restart: unless-stopped
    volumes:
      # 挂载Docker socket以管理其他容器
      - /var/run/docker.sock:/var/run/docker.sock
      # 数据持久化
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - API_HOST=0.0.0.0
      - API_PORT=8100
      - CORS_ORIGINS=http://localhost,http://localhost:80
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8100/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Redis - 缓存和会话存储
  redis:
    image: redis:7-alpine
    container_name: algorithm-platform-redis
    ports:
      - "6379:6379"
    networks:
      - algorithm-platform
    restart: unless-stopped
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes

networks:
  algorithm-platform:
    driver: bridge
    name: algorithm-platform-network

volumes:
  redis_data:
    driver: local
