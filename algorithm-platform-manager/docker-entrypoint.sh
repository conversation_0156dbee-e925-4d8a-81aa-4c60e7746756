#!/bin/bash
set -e

# 获取Docker socket的组ID
DOCKER_SOCK_GID=$(stat -c "%g" /var/run/docker.sock 2>/dev/null || echo "0")

# 如果Docker socket存在，确保appuser用户可以访问
if [ -S /var/run/docker.sock ]; then
    echo "Docker socket found, setting up permissions..."
    
    # 创建docker组（如果不存在）
    if ! getent group docker > /dev/null 2>&1; then
        groupadd -g $DOCKER_SOCK_GID docker
    fi
    
    # 将appuser添加到docker组
    usermod -aG docker appuser
    
    echo "Docker permissions configured successfully"
else
    echo "Warning: Docker socket not found at /var/run/docker.sock"
fi

# 切换到appuser用户并启动应用
echo "Starting application as appuser..."
exec gosu appuser "$@"
