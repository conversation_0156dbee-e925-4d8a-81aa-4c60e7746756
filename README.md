# 永嘉交通事故检测系统 - 算法集合

## 🎯 项目概述

这是一个包含多个独立AI算法的交通事故检测系统。每个算法都是完全独立的包，可以单独复制给其他人使用。

## 📁 项目结构

```
yongjia_traffic_accident/
├── 📂 algorithms/                    # 独立算法包目录
│   ├── renchefei/                   # 人车非检测算法 (完全独立)
│   │   ├── src/                     # 源代码
│   │   ├── scripts/                 # 独立脚本
│   │   ├── data/                    # 数据目录
│   │   ├── logs/                    # 日志目录
│   │   ├── models/                  # 模型文件
│   │   ├── pyproject.toml           # uv 配置
│   │   └── README.md                # 独立文档
│   │
│   ├── accident_classify/           # 事故分类算法 (完全独立)
│   │   ├── src/                     # 源代码
│   │   ├── scripts/                 # 独立脚本
│   │   ├── data/                    # 数据目录
│   │   ├── logs/                    # 日志目录
│   │   ├── models/                  # 模型文件
│   │   ├── pyproject.toml           # uv 配置
│   │   └── README.md                # 独立文档
│   │
│   ├── wenzhou_face/                # 温州人脸识别算法 (完全独立)
│   │   ├── src/                     # 源代码
│   │   ├── scripts/                 # 独立脚本
│   │   ├── data/                    # 数据目录
│   │   ├── logs/                    # 日志目录
│   │   ├── models/                  # 模型文件
│   │   ├── pyproject.toml           # uv 配置
│   │   └── README.md                # 独立文档
│   │
│   └── data/                        # 共享数据目录
│       └── input/                   # 测试输入数据
│
└── README.md                        # 总项目说明 (本文件)
```

## 🚀 算法包列表

### 1. 人车非检测算法 (RenCheFei Detection)
- **位置**: `algorithms/renchefei/`
- **功能**: 基于 YOLOv5 的人员、车辆、非机动车检测
- **特点**: 高精度目标检测，支持实时处理
- **使用**: 查看 `algorithms/renchefei/README.md`

### 2. 事故分类算法 (Accident Classification)
- **位置**: `algorithms/accident_classify/`
- **功能**: 交通事故类型自动分类
- **特点**: 多类别分类，支持严重程度判断
- **使用**: 查看 `algorithms/accident_classify/README.md`

### 3. 温州人脸识别算法 (Wenzhou Face Recognition)
- **位置**: `algorithms/wenzhou_face/`
- **功能**: 人脸检测、识别和质量评估
- **特点**: 高精度人脸识别，支持质量评估
- **使用**: 查看 `algorithms/wenzhou_face/README.md`

## 🎯 独立算法包特性

### ✅ 完全独立
- 每个算法包都可以单独复制给其他人
- 包含完整的开发、部署、文档
- 不依赖项目根目录的任何文件

### ✅ 统一架构
- 相同的目录结构和脚本命名
- 统一的开发→同步→Docker→部署流程
- 一致的日志系统和配置管理

### ✅ 环境隔离
- 每个算法包有独立的 uv 虚拟环境
- 独立的依赖管理和版本控制
- 互不干扰的运行环境

## 🛠️ 使用指南

### 快速开始某个算法
```bash
# 进入算法目录
cd algorithms/renchefei/

# 使用交互式菜单
./scripts/quick_start.sh

# 或者直接运行
cd dev
uv run python run_inference.py ../data/input/test.jpg
```

### 复制算法包给其他人
```bash
# 直接复制整个算法目录
cp -r algorithms/renchefei/ /path/to/destination/

# 对方可以直接使用
cd /path/to/destination/renchefei/
./scripts/quick_start.sh
```

### 添加新算法包
1. **创建目录结构**:
   ```bash
   mkdir -p algorithms/new_algorithm/{dev,docker,scripts,data/{input,output},logs}
   ```

2. **复制模板文件**:
   ```bash
   cp algorithms/renchefei/scripts/* algorithms/new_algorithm/scripts/
   cp algorithms/renchefei/pyproject.toml algorithms/new_algorithm/
   cp algorithms/renchefei/README.md algorithms/new_algorithm/
   ```

3. **修改配置**:
   - 更新脚本中的算法名称
   - 修改 README 文档
   - 调整 pyproject.toml 配置

## 📋 开发流程

### 标准开发流程 (每个算法包)
```bash
# 1. 本地开发
cd algorithms/your_algorithm/dev
# 修改代码...

# 2. 本地测试
uv run python run_inference.py ../data/input/test.jpg

# 3. 同步到 Docker
./scripts/sync_to_docker.sh

# 4. 构建镜像
./scripts/build_docker.sh

# 5. 部署测试
./scripts/deploy.sh

# 6. 一键重新部署
./scripts/deploy.sh --rebuild
```

### 并行开发
- 多个算法包可以同时开发
- 每个包有独立的 Docker 容器
- 不会相互影响

## 🔧 系统要求

### 本地开发
- **操作系统**: macOS, Linux, Windows
- **Python**: 3.8+
- **包管理器**: uv (推荐)
- **Docker**: 20.10+ (用于部署)

### 生产部署
- **Docker**: 20.10+
- **内存**: 8GB+ (推荐)
- **存储**: 20GB+ (包含所有算法)
- **GPU**: 可选，支持 CUDA 和 MPS

## 📈 项目优势

### 🎯 模块化设计
- 每个算法都是独立模块
- 可以单独维护和升级
- 便于团队协作开发

### 🚀 快速部署
- 标准化的部署流程
- 一键构建和部署
- 支持多环境部署

### 📊 专业级质量
- 完善的日志系统
- 性能监控和统计
- 错误处理和恢复

### 🔄 易于扩展
- 标准化的算法包结构
- 便于添加新算法
- 支持不同技术栈

## 📞 技术支持

### 算法包相关问题
- 查看对应算法包的 README.md
- 检查算法包的日志文件
- 运行算法包的 quick_start.sh 诊断

### 系统架构问题
- 检查 Docker 环境
- 验证网络配置
- 查看容器运行状态

## 🎉 总结

这个项目实现了**完全独立的算法包架构**：

- ✅ **即插即用** - 每个算法包都可以单独使用
- ✅ **标准化** - 统一的开发和部署流程
- ✅ **专业级** - 完善的日志、监控、错误处理
- ✅ **可扩展** - 便于添加新算法和功能

**每个算法包都是一个完整的、可独立运行的AI系统！** 🚀
