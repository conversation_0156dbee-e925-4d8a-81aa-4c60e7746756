# 通用AI算法管理平台 - 统一开发指南 (v2.0)

**文档状态**: 权威指南，持续更新  
**当前版本**: 2.0 (基于v2.0统一API响应规范)  
**文档更新**: 2025-07-30

---

## 1. 🎯 项目概述

这是一个为各类AI算法设计的、通用的算法管理与部署平台。平台提供标准化的开发规范、容器化的部署方案、统一的Web管理界面以及在线测试工具，旨在赋能算法开发者，加速AI应用的落地与迭代。

### 核心价值
- **标准化**: 为所有算法提供统一的开发、部署和API调用规范。
- **平台化**: 通过Web管理平台集中管理所有算法服务的生命周期。
- **模块化**: 每个算法被封装为独立、可插拔的微服务。
- **易用性**: 提供在线测试、自动文档和一键启停等便捷功能。
- **专业架构**: 采用现代化的前后端分离架构和微服务理念。

### 项目状态快照 (截至 2025-07-29)
- ✅ **核心管理平台**: 专业版已完成，功能可用。
- ✅ **示例算法 `face_detection`**: 已完成v1.0 API化改造。
- ⏳ **待办事项**: 将现有算法（如人车非检测）按最新的V2.0规范进行重构和集成。
- 🚀 **开发规范**: 已全面升级至 **v2.0统一响应格式规范**。

---

## 2. 🏛️ 系统架构与技术栈

### 2.1. 整体项目结构 (建议)
Use code with caution.
Markdown
ai_algorithm_platform/
├── 📂 algorithms/ # 独立算法包目录
│ ├── 📂 face_detection/ # 示例算法: 人脸检测
│ ├── 📂 object_detection/ # 示例算法: 通用目标检测
│ └── 📂 ... # 其他算法
├── 📂 algorithm-platform-manager/ # 算法管理平台 (已完成)
│ ├── 📂 frontend/ # Vue 3前端
│ ├── 📂 backend/ # FastAPI后端
│ └── 📂 scripts/ # 部署脚本
├── 📄 UNIFIED_AI_PLATFORM_DEVELOPMENT_GUIDE.md # (本文档) 权威开发指南
└── 📄 README.md # 简版项目说明
Generated code
### 2.2. 技术栈
- **前端**: Vue 3.3+, Element Plus 2.4+, Vite 5.4+, Pinia 2+
- **后端 (管理平台)**: Python 3.11, FastAPI 0.104+, Docker API
- **后端 (算法服务)**: **Python 3.11+, FastAPI, Pydantic v2**
- **包管理器**: **uv (强制使用)**
- **部署**: **Docker 20.10+, Docker Compose**

### 2.3. 端口分配规范
- **算法API服务**: 8001-8099
- **平台管理API**: 8100
- **前端服务**: 3000

---

## 3. 🔧 AI开发核心规范 (V2.0)

**所有新开发的算法或对现有算法的重构，必须严格遵守以下V2.0规范。**

### 3.1. 统一响应格式 (Canonical Response Format)

所有API端点必须返回此结构。这是保证平台兼容性的核心。

```json
{
  "success": true,
  "error": null,
  "data": { /* 业务数据 */ },
  "metadata": { /* 诊断元数据 */ }
}
Use code with caution.
3.1.1. 成功响应 (success: true)
以检测算法为例：
Generated json
{
  "success": true,
  "error": null,
  "data": {
    "detections": [
      {
        "bbox": [132, 98, 308, 336],
        "confidence": 0.8829,
        "label": "face",
        "class_id": 0,
        "center": [220, 217],
        "width": 176,
        "height": 237,
        "attributes": {
          "feature_vector": [0.1, ...],
          "quality_score": 0.95
        }
      }
    ],
    "summary": {
      "num_detections": 1,
      "class_counts": { "face": 1 },
      "confidence_stats": { "min": 0.88, "max": 0.88, "avg": 0.88 }
    }
  },
  "metadata": {
    "processing_time_ms": 802.68,
    "image_shape": [523, 440, 3],
    "timestamp_utc": "2025-07-30T12:55:00.123Z",
    "model_info": { "name": "yolov8n", "version": "1.0.0" },
    "hardware_info": { "device": "cuda:0", "memory_used_mb": 1024 }
  }
}
```

关键设计原则:
bbox 统一为 [x_min, y_min, x_max, y_max]。
类别名统一为 label。
算法特定数据放入 attributes 字典。
3.1.2. 错误响应 (success: false)
Generated json
{
  "success": false,
  "error": {
    "code": "INVALID_FILE_FORMAT",
    "message": "不支持的文件格式",
    "details": "支持的格式: jpg, png",
    "timestamp": "2025-07-30T12:55:00.123Z"
  },
  "data": null,
  "metadata": { /* ... */ }
}
Use code with caution.
Json
3.2. 核心API接口规范
每个算法服务必须实现以下 必需端点:
端点	方法	功能	状态
/api/v1/health	GET	健康检查	必需
/api/v1/info	GET	算法信息	必需
/api/v1/{function}	POST	核心功能接口 (如detect, classify)	必需
/docs	GET	OpenAPI自动文档	必需
3.3. 算法项目结构规范
Generated code
algorithms/{algorithm_name}/
├── pyproject.toml              # 项目配置 (uv)
├── uv.lock                     # 依赖锁定文件
├── Dockerfile                  # 容器配置
├── README.md                   # 算法说明
├── src/
│   ├── api_server.py           # FastAPI应用入口
│   ├── models/                 # Pydantic数据模型 (含统一响应模型)
│   ├── core/                   # 核心算法逻辑 (推理引擎)
│   └── utils/                  # 工具函数
├── models/                     # 模型权重文件
└── tests/                      # 测试脚本
Use code with caution.
3.4. Docker容器化规范
3.4.1. 必需的Docker标签
为了能被管理平台自动发现和管理，Dockerfile中必须包含以下标签：
Generated dockerfile
LABEL algorithm.platform="true"
LABEL algorithm.name="算法中文名"
LABEL algorithm.type="目标检测"
LABEL algorithm.version="2.0.0"
LABEL algorithm.description="算法的详细功能描述"
Use code with caution.
Dockerfile
3.4.2. 标准Dockerfile模板
Generated dockerfile
FROM python:3.11-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    curl libgl1-mesa-glx libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# 安装uv
RUN pip install --no-cache-dir uv

# 安装Python依赖
COPY pyproject.toml uv.lock ./
RUN uv sync --frozen --no-dev

# 复制源代码
COPY src/ ./src/
COPY models/ ./models/

# 创建并使用非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

EXPOSE 8000

# 健康检查 (与API端点对应)
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s \
    CMD curl -f http://localhost:8000/api/v1/health || exit 1

CMD ["uv", "run", "python", "src/api_server.py"]